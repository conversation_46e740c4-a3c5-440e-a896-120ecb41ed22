# Database Configuration
DATABASE_TYPE=sqlite
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_USERNAME=postgres
DATABASE_PASSWORD=password
DATABASE_NAME=hungdong_pos
DATABASE_SYNCHRONIZE=false
DATABASE_LOGGING=false
DATABASE_SSL=false

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=1h
JWT_REFRESH_EXPIRES_IN=7d

# Application Configuration
NODE_ENV=development
PORT=3001
API_PREFIX=api
API_VERSION=v1

# MiniSearch Configuration
# MiniSearch is an in-memory search engine, no external server required
# These settings control search behavior and performance

# Search Configuration
SEARCH_DEFAULT_PER_PAGE=20
SEARCH_MAX_PER_PAGE=100
SEARCH_FUZZY_THRESHOLD=0.2
SEARCH_PREFIX_SEARCH=true
SEARCH_ENABLE_SUGGESTIONS=true
SEARCH_ENABLE_ANALYTICS=false
SEARCH_CACHE_DURATION=300
SEARCH_MAX_SUGGESTIONS=10
SEARCH_COMBINE_WITH=AND
SEARCH_MAX_DOCUMENTS=100000
SEARCH_MEMORY_LIMIT=512
SEARCH_ENABLE_COMPRESSION=true
SEARCH_ENABLE_CACHING=true

# Field Boost Configuration
SEARCH_BOOST_NAME=2.0
SEARCH_BOOST_SKU=1.5
SEARCH_BOOST_CATEGORY=1.2
SEARCH_BOOST_DESCRIPTION=1.0
SEARCH_BOOST_KEYWORDS=1.3
SEARCH_BOOST_BARCODE=1.8

# Indexing Configuration
INDEXING_BATCH_SIZE=100
INDEXING_RETRY_ATTEMPTS=3
INDEXING_RETRY_DELAY=1000
INDEXING_ENABLE_AUTO_REINDEX=false
INDEXING_REINDEX_INTERVAL=24
INDEXING_ENABLE_INCREMENTAL_SYNC=true
INDEXING_SYNC_INTERVAL=5

# Monitoring Configuration
MONITORING_ENABLE_METRICS=true
MONITORING_METRICS_INTERVAL=60
MONITORING_ENABLE_HEALTH_CHECK=true
MONITORING_HEALTH_CHECK_INTERVAL=30
MONITORING_SEARCH_LATENCY_THRESHOLD=1000
MONITORING_INDEXING_ERROR_THRESHOLD=5
MONITORING_CONNECTION_FAILURE_THRESHOLD=10

# MiniSearch Collection Configuration
# No external collection management needed - data is loaded from database automatically
