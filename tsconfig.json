{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2023", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": true, "forceConsistentCasingInFileNames": true, "noImplicitAny": false, "strictBindCallApply": false, "noFallthroughCasesInSwitch": false, "paths": {"@/*": ["src/*"], "@/common/*": ["src/common/*"], "@/config/*": ["src/config/*"], "@/modules/*": ["src/modules/*"], "@/errors/*": ["src/errors/*"], "@/filters/*": ["src/filters/*"], "@/interceptors/*": ["src/interceptors/*"], "@/decorators/*": ["src/decorators/*"], "@/guards/*": ["src/guards/*"], "@/types/*": ["src/types/*"], "@/constants/*": ["src/constants/*"]}}}