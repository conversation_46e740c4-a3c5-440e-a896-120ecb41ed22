{"name": "hungdong-pos-server", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "node scripts/ensure-i18n-types.js && nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "node scripts/ensure-i18n-types.js && nest start", "start:dev": "node scripts/ensure-i18n-types.js && nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest --passWithNoTests", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "test:unit": "jest --testPathPattern=src/.*\\.spec\\.ts$", "test:integration": "jest --config ./test/jest-e2e.json --testPathPattern=integration\\.spec\\.ts$", "test:e2e:only": "jest --config ./test/jest-e2e.json --testPathPattern=e2e-spec\\.ts$", "test:all": "npm run test:unit && npm run test:integration && npm run test:e2e:only", "test:ci": "jest --coverage --watchAll=false && jest --config ./test/jest-e2e.json --watchAll=false", "test:runner": "node scripts/test-runner.js", "build:pkg": "npm run build && pkg . --targets node18-win-x64 --output hungdong-pos-server.exe", "build:pkg:all": "npm run build && pkg . --targets node18-win-x64,node18-macos-x64,node18-linux-x64 --out-path ./dist-pkg", "test:related": "jest --bail --findRelatedTests", "prepare": "husky install", "commit": "git-cz", "db:create": "mkdir -p data", "db:migrate": "typeorm-ts-node-commonjs migration:run -d src/config/database.config.ts", "db:migrate:revert": "typeorm-ts-node-commonjs migration:revert -d src/config/database.config.ts", "db:migrate:generate": "typeorm-ts-node-commonjs migration:generate -d src/config/database.config.ts", "db:migrate:create": "typeorm-ts-node-commonjs migration:create src/core/database/migrations/$npm_config_name", "db:migrate:show": "typeorm-ts-node-commonjs migration:show -d src/config/database.config.ts", "db:seed:all": "node scripts/seed-all-data.js", "db:check": "node scripts/check-seed-data.js", "db:verify": "node scripts/verify-database.js", "migration:create": "node scripts/create-migration.js"}, "dependencies": {"@nestjs/common": "^11.0.1", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.1", "@nestjs/event-emitter": "^3.0.1", "@nestjs/jwt": "^11.0.0", "@nestjs/mapped-types": "^2.1.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.1", "@nestjs/schedule": "^6.0.0", "@nestjs/serve-static": "^5.0.3", "@nestjs/swagger": "^11.2.0", "@nestjs/throttler": "^6.4.0", "@nestjs/typeorm": "^11.0.0", "bcryptjs": "^3.0.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "csv-parser": "^3.2.0", "fast-csv": "^5.0.2", "helmet": "^8.1.0", "minisearch": "^7.1.2", "moment": "^2.30.1", "multer": "^2.0.1", "nest-commander": "^3.18.0", "nestjs-i18n": "^10.5.1", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "sharp": "^0.34.3", "sqlite3": "^5.1.7", "swagger-ui-express": "^5.0.1", "typeorm": "^0.3.24", "typeorm-extension": "^3.6.1", "uuid": "^11.1.0"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/bcryptjs": "^3.0.0", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/multer": "^2.0.0", "@types/node": "^22.10.7", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "@types/supertest": "^6.0.2", "@types/uuid": "^10.0.0", "commitizen": "^4.3.1", "concurrently": "^9.2.0", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^16.0.0", "husky": "^9.1.7", "jest": "^29.7.0", "lint-staged": "^16.1.0", "pkg": "^5.8.1", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node", "moduleNameMapper": {"^@/(.*)$": "<rootDir>/$1"}}, "bin": "start.js", "pkg": {"scripts": ["dist/**/*.js"], "assets": ["node_modules/sqlite3/lib/binding/**/*", "node_modules/sharp/build/Release/**/*", "node_modules/sharp/vendor/**/*", "node_modules/@img/**/*", "dist/i18n/**/*", ".env"], "targets": ["node18-win-x64", "node18-macos-x64", "node18-linux-x64"], "outputPath": "dist-pkg", "options": ["--no-bytecode", "--public-packages=*"]}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["eslint --fix", "prettier --write"], "$schema": "https://json.schemastore.org/lintstaged"}}