# Dependencies
node_modules/

# Build outputs
dist/
build/
coverage/

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Database
*.db
*.sqlite

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE
.vscode/
.idea/

# OS
.DS_Store
Thumbs.db

# Package files
*.tgz
*.tar.gz

# Executable
*.exe

# Generated files
*.d.ts.map
*.js.map

# Lock files
package-lock.json
yarn.lock

# Misc
.eslintcache
