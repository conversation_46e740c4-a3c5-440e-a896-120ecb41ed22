#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function runCommand(command, description) {
  log(`\n${colors.bright}${colors.blue}Running: ${description}${colors.reset}`);
  log(`${colors.cyan}Command: ${command}${colors.reset}`);
  
  try {
    execSync(command, {
      stdio: 'inherit',
      cwd: process.cwd()
    });
    log(`${colors.green}✓ ${description} completed successfully${colors.reset}`);
    return true;
  } catch (error) {
    log(`${colors.red}✗ ${description} failed${colors.reset}`);
    return false;
  }
}

function checkTestFiles() {
  const testDirs = [
    'src/modules',
    'test'
  ];
  
  let totalTests = 0;
  
  testDirs.forEach(dir => {
    if (fs.existsSync(dir)) {
      const files = execSync(`find ${dir} -name "*.spec.ts" -o -name "*.e2e-spec.ts"`, { encoding: 'utf8' });
      const testFiles = files.trim().split('\n').filter(f => f);
      totalTests += testFiles.length;
      
      log(`${colors.yellow}Found ${testFiles.length} test files in ${dir}:${colors.reset}`);
      testFiles.forEach(file => {
        log(`  - ${file}`);
      });
    }
  });
  
  log(`${colors.bright}${colors.magenta}Total test files: ${totalTests}${colors.reset}\n`);
  return totalTests;
}

function generateTestReport() {
  log(`${colors.bright}${colors.cyan}Generating test coverage report...${colors.reset}`);
  
  const coverageDir = 'coverage';
  if (fs.existsSync(coverageDir)) {
    const htmlReport = path.join(coverageDir, 'lcov-report', 'index.html');
    if (fs.existsSync(htmlReport)) {
      log(`${colors.green}Coverage report available at: ${htmlReport}${colors.reset}`);
    }
  }
}

function main() {
  const args = process.argv.slice(2);
  const testType = args[0] || 'all';
  
  log(`${colors.bright}${colors.magenta}🧪 POS System Test Runner${colors.reset}`);
  log(`${colors.bright}${colors.magenta}=========================${colors.reset}\n`);
  
  // Check test files
  const testCount = checkTestFiles();
  if (testCount === 0) {
    log(`${colors.red}No test files found!${colors.reset}`);
    process.exit(1);
  }
  
  let success = true;
  
  switch (testType) {
    case 'unit':
      success = runCommand('npm run test:unit', 'Unit Tests');
      break;
      
    case 'integration':
      success = runCommand('npm run test:integration', 'Integration Tests');
      break;
      
    case 'e2e':
      success = runCommand('npm run test:e2e:only', 'End-to-End Tests');
      break;
      
    case 'coverage':
      success = runCommand('npm run test:cov', 'Unit Tests with Coverage');
      if (success) {
        generateTestReport();
      }
      break;
      
    case 'ci':
      success = runCommand('npm run test:ci', 'CI Tests (Unit + Integration + E2E)');
      if (success) {
        generateTestReport();
      }
      break;
      
    case 'all':
    default:
      log(`${colors.bright}${colors.yellow}Running all test suites...${colors.reset}`);
      
      const unitSuccess = runCommand('npm run test:unit', 'Unit Tests');
      const integrationSuccess = runCommand('npm run test:integration', 'Integration Tests');
      const e2eSuccess = runCommand('npm run test:e2e:only', 'End-to-End Tests');
      
      success = unitSuccess && integrationSuccess && e2eSuccess;
      break;
  }
  
  log(`\n${colors.bright}${colors.magenta}Test Summary${colors.reset}`);
  log(`${colors.bright}${colors.magenta}============${colors.reset}`);
  
  if (success) {
    log(`${colors.bright}${colors.green}🎉 All tests passed!${colors.reset}`);
    log(`${colors.green}✓ Test suite completed successfully${colors.reset}`);
  } else {
    log(`${colors.bright}${colors.red}❌ Some tests failed!${colors.reset}`);
    log(`${colors.red}✗ Please check the output above for details${colors.reset}`);
  }
  
  // Test statistics
  log(`\n${colors.bright}${colors.cyan}Test Statistics:${colors.reset}`);
  log(`${colors.cyan}• Total test files: ${testCount}${colors.reset}`);
  log(`${colors.cyan}• Test types: Unit, Integration, E2E${colors.reset}`);
  log(`${colors.cyan}• Coverage: Available with 'npm run test:cov'${colors.reset}`);
  
  // Usage information
  log(`\n${colors.bright}${colors.yellow}Usage:${colors.reset}`);
  log(`${colors.yellow}  node scripts/test-runner.js [type]${colors.reset}`);
  log(`${colors.yellow}  Types: unit, integration, e2e, coverage, ci, all${colors.reset}`);
  
  process.exit(success ? 0 : 1);
}

if (require.main === module) {
  main();
}

module.exports = {
  runCommand,
  checkTestFiles,
  generateTestReport,
};
