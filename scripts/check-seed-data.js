#!/usr/bin/env node

const sqlite3 = require('sqlite3').verbose();
const path = require('path');

async function checkSeedData() {
  const dbPath = path.join(process.cwd(), 'data', 'hungdong-pos.sqlite');

  console.log('📊 Checking seeded data...');
  console.log(`📁 Database: ${dbPath}`);

  const db = new sqlite3.Database(dbPath);

  try {
    const tables = ['users', 'categories', 'products', 'inventory', 'orders', 'order_items'];

    for (const table of tables) {
      const count = await new Promise((resolve, reject) => {
        db.get(`SELECT COUNT(*) as count FROM ${table}`, (err, row) => {
          if (err) reject(err);
          else resolve(row.count);
        });
      });

      console.log(`📋 ${table}: ${count} records`);
    }

    // Show some sample data
    console.log('\n📝 Sample data:');

    // Sample users
    const users = await new Promise((resolve, reject) => {
      db.all('SELECT email, firstName, lastName, position FROM users LIMIT 3', (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    console.log('\n👥 Sample Users:');
    users.forEach(user => {
      console.log(`   - ${user.firstName} ${user.lastName} (${user.email}) - ${user.position}`);
    });

    // Sample categories
    const categories = await new Promise((resolve, reject) => {
      db.all('SELECT name, color FROM categories LIMIT 5', (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    console.log('\n🏷️  Sample Categories:');
    categories.forEach(category => {
      console.log(`   - ${category.name} (${category.color})`);
    });

    // Sample products
    const products = await new Promise((resolve, reject) => {
      db.all('SELECT name, sku, price FROM products LIMIT 5', (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    console.log('\n📦 Sample Products:');
    products.forEach(product => {
      console.log(`   - ${product.name} (${product.sku}) - $${product.price}`);
    });

    // Sample orders
    const orders = await new Promise((resolve, reject) => {
      db.all('SELECT orderNumber, customerName, status, total FROM orders LIMIT 5', (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    console.log('\n🛒 Sample Orders:');
    orders.forEach(order => {
      const customer = order.customerName || 'Walk-in Customer';
      console.log(`   - ${order.orderNumber} (${customer}) - ${order.status} - $${order.total}`);
    });

    console.log('\n✅ Data verification complete!');
  } catch (error) {
    console.error('❌ Error checking data:', error.message);
    process.exit(1);
  } finally {
    db.close();
  }
}

checkSeedData();
