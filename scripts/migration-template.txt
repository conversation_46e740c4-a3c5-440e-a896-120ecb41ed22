import { MigrationInterface, QueryRunner, Table } from 'typeorm';

export class {{MIGRATION_CLASS_NAME}} implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: '',
        columns: [],
      }),
      true,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop table
    await queryRunner.dropTable('');
  }
}
