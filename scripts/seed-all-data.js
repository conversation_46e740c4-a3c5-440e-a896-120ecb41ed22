#!/usr/bin/env node

const sqlite3 = require('sqlite3').verbose();
const bcrypt = require('bcryptjs');
const path = require('path');

// Sample data
const sampleData = {
  categories: [
    { name: '<PERSON><PERSON> củ, tr<PERSON>i cây', description: '<PERSON><PERSON> củ, tr<PERSON>i cây ...' },
    { name: 'Bánh, kẹo, snack', description: 'Bánh, kẹo, snack' },
    { name: 'Thức uống', description: 'Thức uống' },
    { name: '<PERSON><PERSON> vị, gạo, thực phẩm khô', description: '<PERSON><PERSON> vị, gạo, thực phẩm khô' },
    { name: '<PERSON>ă<PERSON> sóc cá nhân', description: 'Chăm sóc cá nhân' },
  ],
  users: ['admin', 'user1', 'user2', 'user3'],
  customerNames: ['Khách hàng vãng lai'],
  paymentMethods: ['cash', 'card', 'digital'],
  orderStatuses: ['pending', 'completed', 'cancelled'],
  orderNotes: [
    '<PERSON>ản phẩm bị bể vỡ, hư hỏng trước khi đến tay Khách Hàng',
    '<PERSON><PERSON>o hàng trễ',
    'Khách Hàng không ưng ý với sản phẩm',
    'Khách Hàng nhầm lẫn trong việc đặt hàng',
    'Giao hàng không thành công 2 lần',
    'Không liên hệ được với Khách Hàng để giao hàng 3 lần',
    'Giao hàng thiếu sản phẩm khuyến mãi',
    'Giao hàng thiếu phụ kiện đi kèm',
    'Sản phẩm giao sai số lượng',
  ],
};

const utils = {
  slugify: str =>
    str
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '')
      .toLowerCase()
      .replace(/[^\w\s-]/g, '')
      .trim()
      .replace(/\s+/g, '-'),
  bcryptPassword: password => bcrypt.hash(password, 12),
};

async function seedDatabase() {
  const dbPath = path.join(process.cwd(), 'data', 'hungdong-pos.sqlite');
  console.log('🌱 Seeding all database tables...');
  console.log(`📁 Database: ${dbPath}`);

  const db = new sqlite3.Database(dbPath);

  try {
    const existingData = await runQuery(db, 'SELECT COUNT(*) as count FROM products');

    if (existingData.count > 0) {
      console.warn('⚠️ Database already seeded. Skipping...');
      return;
    }

    const categoryIds = await seedCategories(db);
    const userIds = await seedUsers(db);
    const productIds = await seedProducts(db, categoryIds);

    console.log('\n🎉 Seeding complete!');
    console.log(`\n📊 Summary:\n
      - Categories: ${categoryIds.length}\n
      - Users: ${userIds.length}\n
      - Products: ${productIds.length}\n`);
  } catch (error) {
    console.error('❌ Error seeding database:', error.message);
    process.exit(1);
  } finally {
    db.close();
  }
}

function runQuery(db, query, params = [], mode = 'get') {
  return new Promise((resolve, reject) => {
    if (mode === 'get') {
      db.get(query, params, (err, row) => (err ? reject(err) : resolve(row)));
    } else if (mode === 'all') {
      db.all(query, params, (err, rows) => (err ? reject(err) : resolve(rows)));
    }
  });
}

function runInsert(db, query, params = [], returnId = null) {
  return new Promise((resolve, reject) => {
    db.run(query, params, function (err) {
      if (err) reject(err);
      else resolve(returnId || this.lastID);
    });
  });
}

async function seedCategories(db) {
  console.log('🏷️  Seeding categories...');
  const ids = [];
  for (const [i, cat] of sampleData.categories.entries()) {
    const id = await runInsert(
      db,
      `INSERT INTO categories (name, slug, description, isActive, sortOrder) VALUES (?, ?, ?, ?, ?)`,
      [cat.name, utils.slugify(cat.name), cat.description, 1, i + 1],
    );
    ids.push(id);
  }
  return ids;
}

async function seedUsers(db) {
  console.log('👥 Seeding users...');
  const ids = [];
  const password = await utils.bcryptPassword('123123');
  for (const username of sampleData.users) {
    const id = await runInsert(
      db,
      `INSERT INTO users (username, password, isActive)
      VALUES (?, ?, ?)`,
      [username, password, 1],
    );
    ids.push(id);
  }
  return ids;
}

async function seedProducts(db, categoryIds) {
  console.log('📦 Seeding products...');
  const ids = [];
  const productImportRecords = await runQuery(db, 'SELECT * FROM product_import_records LIMIT 20', [], 'all');

  if (productImportRecords.length > 0) {
    for (const record of productImportRecords) {
      const i = Math.floor(Math.random() * categoryIds.length);
      const id = await runInsert(
        db,
        `INSERT INTO products (id, name, description, price, tax, categoryId, isActive)
        VALUES (?, ?, ?, ?, ?, ?, ?)`,
        [record.id, record.name, record.description, record.price, record.tax, categoryIds[i], record.isActive],
      );
      ids.push(id);
    }
    return ids;
  }
}

seedDatabase();
