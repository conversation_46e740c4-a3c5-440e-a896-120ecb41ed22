#!/usr/bin/env node

const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const dbPath = path.join(process.cwd(), 'data', 'hungdong-pos.sqlite');

console.log('🔍 Verifying database structure...');
console.log(`📁 Database path: ${dbPath}`);

const db = new sqlite3.Database(dbPath, err => {
  if (err) {
    console.error('❌ Error opening database:', err.message);
    process.exit(1);
  }
  console.log('✅ Connected to SQLite database');
});

// Check if users table exists and show its structure
db.all("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name;", (err, tables) => {
  if (err) {
    console.error('❌ Error fetching tables:', err.message);
    return;
  }

  console.log('\n📋 Tables in database:');
  tables.forEach(table => {
    console.log(`  - ${table.name}`);
  });

  // Show users table structure
  db.all('PRAGMA table_info(users);', (err, columns) => {
    if (err) {
      console.error('❌ Error fetching users table info:', err.message);
      return;
    }

    if (columns.length > 0) {
      console.log('\n👤 Users table structure:');
      columns.forEach(col => {
        const nullable = col.notnull ? 'NOT NULL' : 'NULL';
        const defaultVal = col.dflt_value ? ` DEFAULT ${col.dflt_value}` : '';
        const primary = col.pk ? ' PRIMARY KEY' : '';
        console.log(`  - ${col.name}: ${col.type}${primary} ${nullable}${defaultVal}`);
      });
    } else {
      console.log('\n❌ Users table not found');
    }

    // Show indexes
    db.all("SELECT name, sql FROM sqlite_master WHERE type='index' AND tbl_name='users';", (err, indexes) => {
      if (err) {
        console.error('❌ Error fetching indexes:', err.message);
        return;
      }

      if (indexes.length > 0) {
        console.log('\n🔍 Indexes on users table:');
        indexes.forEach(index => {
          if (index.name && !index.name.startsWith('sqlite_')) {
            console.log(`  - ${index.name}`);
          }
        });
      }

      // Show migration history
      db.all('SELECT * FROM migrations ORDER BY timestamp;', (err, migrations) => {
        if (err) {
          console.error('❌ Error fetching migrations:', err.message);
        } else {
          console.log('\n📜 Migration history:');
          migrations.forEach(migration => {
            const date = new Date(migration.timestamp).toISOString();
            console.log(`  - ${migration.name} (${date})`);
          });
        }

        console.log('\n✅ Database verification complete!');
        db.close();
      });
    });
  });
});
