#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Get migration name from command line arguments
const migrationName = process.argv[2];

if (!migrationName) {
  console.error('❌ Error: Migration name is required');
  console.log('📝 Usage: npm run migration:create <MigrationName>');
  console.log('📝 Example: npm run migration:create CreateUsersTable');
  process.exit(1);
}

// Validate migration name (PascalCase)
if (!/^[A-Z][a-zA-Z0-9]*$/.test(migrationName)) {
  console.error('❌ Error: Migration name must be in PascalCase (e.g., CreateUsersTable)');
  process.exit(1);
}

try {
  console.log(`🚀 Creating migration: ${migrationName}`);

  // Generate timestamp
  const timestamp = Date.now();
  const migrationClassName = `${migrationName}${timestamp}`;
  const fileName = `${timestamp}-${migrationName}.ts`;

  // Read template
  const templatePath = path.join(__dirname, 'migration-template.txt');
  let template = fs.readFileSync(templatePath, 'utf8');

  // Replace placeholder with actual class name
  template = template.replace('{{MIGRATION_CLASS_NAME}}', migrationClassName);

  // Create migration file
  const migrationDir = path.join(process.cwd(), 'src/core/database/migrations');
  const migrationFilePath = path.join(migrationDir, fileName);

  // Ensure directory exists
  if (!fs.existsSync(migrationDir)) {
    fs.mkdirSync(migrationDir, { recursive: true });
  }

  // Write migration file
  fs.writeFileSync(migrationFilePath, template);

  console.log(`Migration ${migrationFilePath} has been generated successfully.`);
  console.log('✅ Migration created successfully!');
  console.log('📁 Location: src/core/database/migrations/');
  console.log('📝 Next steps:');
  console.log('   1. Edit the migration file to add your schema changes');
  console.log('   2. Run: npm run db:migrate');
} catch (error) {
  console.error('❌ Error creating migration:', error.message);
  process.exit(1);
}
