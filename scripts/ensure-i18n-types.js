#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

const generatedDir = path.join(process.cwd(), 'dist', 'generated');
const i18nTypesFile = path.join(generatedDir, 'i18n.generated.ts');

if (!fs.existsSync(generatedDir)) {
  fs.mkdirSync(generatedDir, { recursive: true });
  console.log('Created dist/generated directory');
}

if (!fs.existsSync(i18nTypesFile)) {
  const placeholderContent = `/* DO NOT EDIT, file generated by nestjs-i18n */

/* eslint-disable */
/* prettier-ignore */
import { Path } from "nestjs-i18n";
/* prettier-ignore */
export type I18nTranslations = {};
/* prettier-ignore */
export type I18nPath = Path<I18nTranslations>;
`;

  fs.writeFileSync(i18nTypesFile, placeholderContent);
  console.log('Created placeholder i18n.generated.ts file');
} else {
  console.log('i18n.generated.ts file already exists');
}
