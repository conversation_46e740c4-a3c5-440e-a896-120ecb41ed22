# Test Environment Configuration
NODE_ENV=test

# Database Configuration
DB_TYPE=sqlite
DB_DATABASE=:memory:
DB_SYNCHRONIZE=true
DB_LOGGING=false

# JWT Configuration
JWT_SECRET=test-secret-key-for-testing-only
JWT_EXPIRES_IN=1h
JWT_REFRESH_SECRET=test-refresh-secret-key
JWT_REFRESH_EXPIRES_IN=7d

# App Configuration
PORT=3001
API_PREFIX=api

# Logging
LOG_LEVEL=error

# Security
BCRYPT_ROUNDS=4

# Test-specific settings
TEST_TIMEOUT=30000
