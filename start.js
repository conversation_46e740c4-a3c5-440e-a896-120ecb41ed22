#!/usr/bin/env node

const path = require('path');

if (typeof global !== 'undefined' && !global.crypto) {
  global.crypto = require('crypto');
}

// Handle sharp module for packaged environment
if (process.pkg) {
  const originalRequire = require;
  require = function(id) {
    if (id === 'sharp') {
      try {
        // Try to load sharp from the packaged location
        const sharpPath = path.join(path.dirname(process.execPath), 'node_modules', 'sharp');
        return originalRequire(sharpPath);
      } catch (error) {
        console.warn('Sharp module not available in packaged environment:', error.message);
        // Return a mock sharp object that throws meaningful errors
        return {
          default: () => {
            throw new Error('Sharp image processing is not available in this packaged version. Please use the development version for image processing features.');
          }
        };
      }
    }
    return originalRequire(id);
  };
}

process.chdir(path.dirname(process.execPath));
const dbPath = path.join(process.cwd(), 'hungdong-pos.sqlite');
console.log('Database will be created at:', dbPath);

require('./dist/main.js');
