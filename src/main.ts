import { NestFactory, Reflector } from '@nestjs/core';
import { Logger, VersioningType, INestApplication, ClassSerializerInterceptor } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { I18nValidationPipe } from 'nestjs-i18n';
import helmet from 'helmet';
import { join } from 'path';
import { existsSync, mkdirSync } from 'fs';

import { AppModule } from '@/app.module';
import { setupSwagger } from '@/swagger';
import { loadAllErrors } from './core/errors/error-loader';

async function bootstrap(): Promise<void> {
  const logger = new Logger('Bootstrap');

  try {
    await loadAllErrors();
    const app = await NestFactory.create(AppModule);
    const configService = app.get(ConfigService);

    configureSecurityMiddleware(app);
    configureCors(app);
    configureValidation(app);
    configureSerialization(app);
    configureStaticFiles(app, configService);
    configureApiSettings(app);
    setupSwagger(app);

    await startServer(app, configService, logger);
  } catch (error) {
    logger.error('❌ Failed to start server', error);
    process.exit(1);
  }
}

function configureSecurityMiddleware(app: INestApplication): void {
  app.use(
    helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          scriptSrc: ["'self'"],
          imgSrc: ["'self'", 'data:', 'https:'],
        },
      },
      crossOriginEmbedderPolicy: false,
    }),
  );
}

function configureCors(app: INestApplication): void {
  app.enableCors({
    origin: process.env.NODE_ENV === 'production' ? false : true,
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Correlation-ID', 'Accept-Language'],
  });
}

function configureValidation(app: INestApplication): void {
  app.useGlobalPipes(
    new I18nValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    }),
  );
}

function configureSerialization(app: INestApplication): void {
  app.useGlobalInterceptors(new ClassSerializerInterceptor(app.get(Reflector)));
}

function configureApiSettings(app: INestApplication): void {
  app.enableVersioning({
    type: VersioningType.URI,
    defaultVersion: '1',
  });
}

function configureStaticFiles(_app: INestApplication, _configService: ConfigService): void {
  const uploadPath = process.env.UPLOAD_DESTINATION ?? join(process.cwd(), 'uploads');

  if (!existsSync(uploadPath)) {
    mkdirSync(uploadPath, { recursive: true });
  }
}

async function startServer(app: INestApplication, configService: ConfigService, logger: Logger): Promise<void> {
  const port = configService.get<number>('app.port') ?? 3000;
  const appName = configService.get<string>('app.name') ?? 'HungDong POS Server';
  const nodeEnv = configService.get<string>('app.nodeEnv') ?? 'development';

  await app.listen(port);
  logger.log(`🚀 ${appName} is running on: http://localhost:${port}/api/v1`);
  logger.log(`📚 API Documentation: http://localhost:${port}/docs`);
  logger.log(`📍 Environment: ${nodeEnv}`);
}

void bootstrap();
