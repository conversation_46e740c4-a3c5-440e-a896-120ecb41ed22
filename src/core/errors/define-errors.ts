import { ErrorRegistry } from './error.registry';
import { BaseError } from './base.error';

type ErrorGenerator = (...args: any[]) => BaseError;
type ErrorMap = Record<string, ErrorGenerator>;

export function defineErrors(namespace: string, errors: Record<string, (args?: any) => BaseError>): ErrorMap {
  const registeredErrors: ErrorMap = {};

  for (const key of Object.keys(errors)) {
    const fullKey = `${namespace}.${key}`;

    if (ErrorRegistry.get(fullKey)) {
      throw new Error(`❌ Error key already registered: ${fullKey}`);
    }

    const generator = errors[key];
    ErrorRegistry.register(fullKey, generator);
    registeredErrors[key] = generator;
  }

  return registeredErrors;
}
