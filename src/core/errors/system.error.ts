import { HttpStatus } from '@nestjs/common';
import { BaseError } from '@/core/errors/base.error';
import { defineErrors } from '@/core/errors/define-errors';

export const SystemErrors = defineErrors('system', {
  INTERNAL_SERVER_ERROR: (): BaseError => {
    return new BaseError('system.INTERNAL_SERVER_ERROR', {}, [], HttpStatus.INTERNAL_SERVER_ERROR);
  },
  URL_NOT_FOUND: (): BaseError => {
    return new BaseError('system.URL_NOT_FOUND', {}, [], HttpStatus.NOT_FOUND);
  },
  TYPE_ERROR: (): BaseError => {
    return new BaseError('system.TYPE_ERROR', {}, [], HttpStatus.INTERNAL_SERVER_ERROR);
  },
  REFERENCE_ERROR: (): BaseError => {
    return new BaseError('system.REFERENCE_ERROR', {}, [], HttpStatus.INTERNAL_SERVER_ERROR);
  },
  SYNTAX_ERROR: (): BaseError => {
    return new BaseError('system.SYNTAX_ERROR', {}, [], HttpStatus.INTERNAL_SERVER_ERROR);
  },
});
