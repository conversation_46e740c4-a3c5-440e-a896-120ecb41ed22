import { BaseError } from './base.error';

export type ErrorGenerator = (...args: any[]) => BaseError;

export class ErrorRegistry {
  private static registry: Map<string, ErrorGenerator> = new Map();

  static register(key: string, generator: ErrorGenerator): void {
    if (this.registry.has(key)) {
      throw new Error(`Error key "${key}" already registered.`);
    }
    this.registry.set(key, generator);
  }

  static get(key: string): ErrorGenerator | undefined {
    return this.registry.get(key);
  }

  static listKeys(): string[] {
    return Array.from(this.registry.keys());
  }
}
