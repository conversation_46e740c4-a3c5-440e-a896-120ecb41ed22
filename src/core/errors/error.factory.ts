import { HttpStatus, Injectable } from '@nestjs/common';
import { BaseError } from './base.error';
import { ErrorGenerator, ErrorRegistry } from './error.registry';

@Injectable()
export class ErrorFactory {
  static create(key: string, ...args: ErrorGenerator[]): BaseError {
    const generator = ErrorRegistry.get(key);

    if (!generator) {
      throw new Error(`Error key "${key}" is not registered.`);
    }

    return generator(...args);
  }

  mapHttpError(statusCode: HttpStatus): BaseError {
    console.log('statusCode', statusCode);
    switch (statusCode) {
      case HttpStatus.UNAUTHORIZED:
        return ErrorFactory.create('auth.INVALID_CREDENTIALS');
      case HttpStatus.CONFLICT:
        return ErrorFactory.create('auth.USERNAME_ALREADY_EXISTS');
      case HttpStatus.FORBIDDEN:
        return ErrorFactory.create('auth.ACCOUNT_DEACTIVATED');
      case HttpStatus.NOT_FOUND:
        return ErrorFactory.create('system.URL_NOT_FOUND');
      default:
        return ErrorFactory.create('auth.INVALID_CREDENTIALS');
    }
  }

  mapUnexpectedError(error: Error): BaseError {
    if (error.name === 'TypeError') {
      return ErrorFactory.create('system.TYPE_ERROR');
    }

    if (error.name === 'ReferenceError') {
      return ErrorFactory.create('system.REFERENCE_ERROR');
    }

    if (error.name === 'SyntaxError') {
      return ErrorFactory.create('system.SYNTAX_ERROR');
    }

    if (error.message.includes('timeout')) {
      return ErrorFactory.create('system.TIMEOUT_ERROR');
    }

    if (error.message.includes('rate limit')) {
      return ErrorFactory.create('system.RATE_LIMIT_EXCEEDED');
    }

    return ErrorFactory.create('system.INTERNAL_SERVER_ERROR');
  }
}
