import { HttpStatus } from '@nestjs/common';

export interface IErrorContext {
  [key: string]: any;
}

export interface IErrorDetails {
  field?: string;
  value?: any;
  constraint?: string;
  message?: string;
}

export interface IApiErrorResponse {
  error: string;
  message: string[];
  statusCode: number;
}

export class BaseError extends Error {
  public readonly httpStatus: number;
  public readonly context?: IErrorContext;
  public readonly details?: IErrorDetails[];
  public readonly timestamp: string;

  constructor(
    message: string,
    context?: IErrorContext,
    details?: IErrorDetails[],
    status: HttpStatus = HttpStatus.BAD_REQUEST,
  ) {
    super(message);
    this.name = this.constructor.name;
    this.httpStatus = status;
    this.context = context;
    this.details = details;
    this.timestamp = new Date().toISOString();

    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, this.constructor);
    }
  }
}
