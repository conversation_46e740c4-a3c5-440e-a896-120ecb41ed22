import { Injectable, Logger, OnApplicationBootstrap } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { DataSource } from 'typeorm';

export interface MigrationStatus {
  executed: string[];
  pending: string[];
  hasChanges: boolean;
  lastMigration?: string;
  totalMigrations: number;
}

@Injectable()
export class MigrationRunnerService implements OnApplicationBootstrap {
  private readonly logger = new Logger(MigrationRunnerService.name);

  constructor(
    private readonly dataSource: DataSource,
    private readonly configService: ConfigService,
  ) {}

  async onApplicationBootstrap(): Promise<void> {
    const dbConfig = this.configService.get('database');
    const shouldAutoMigrate = dbConfig?.autoMigrate ?? this.configService.get<boolean>('DB_AUTO_MIGRATE', true);

    if (!shouldAutoMigrate) {
      this.logger.log('Auto migration is disabled');
      return;
    }

    try {
      await this.runInitialMigration();
    } catch (error) {
      this.logger.error('Failed to run initial migration', error);
      throw error;
    }
  }

  private async runInitialMigration(): Promise<void> {
    this.logger.debug('Starting database migration process...');

    await this.ensureDatabaseConnection();
    await this.runMigrations();
    await this.showMigrationStatus();

    this.logger.log('Database migration process completed successfully');
  }

  private async ensureDatabaseConnection(): Promise<void> {
    try {
      if (!this.dataSource.isInitialized) {
        await this.dataSource.initialize();
      }

      await this.dataSource.query('SELECT 1');
      this.logger.log('Database connection verified');
    } catch {
      this.logger.log('Database connection failed, attempting to create database...');

      try {
        await this.createDatabaseProgrammatically();

        if (!this.dataSource.isInitialized) {
          await this.dataSource.initialize();
        }

        this.logger.log('Database created and connected successfully');
      } catch (createError) {
        this.logger.error('Failed to create database', createError);
        throw createError;
      }
    }
  }

  private async createDatabaseProgrammatically(): Promise<void> {
    const options = this.dataSource.options;

    if (options.type === 'sqlite') {
      const fs = await import('fs');
      const path = await import('path');

      const dbPath = options.database;
      const dbDir = path.dirname(dbPath);

      if (!fs.existsSync(dbDir)) {
        fs.mkdirSync(dbDir, { recursive: true });
        this.logger.log(`Created database directory: ${dbDir}`);
      }

      this.logger.log(`SQLite database will be created at: ${dbPath}`);
    } else {
      this.logger.log('Cannot create SQLite database');
    }
  }

  private async runMigrations(): Promise<void> {
    try {
      const hasPendingMigrations = await this.dataSource.showMigrations();

      if (!hasPendingMigrations) {
        this.logger.log('No pending migrations found');
        return;
      }

      this.logger.log('Running pending migrations...');

      const executedMigrations = await this.dataSource.runMigrations({
        transaction: 'each',
        fake: false,
      });

      if (executedMigrations.length > 0) {
        this.logger.log(`Successfully executed ${executedMigrations.length} migrations:`);
        executedMigrations.forEach(migration => {
          this.logger.log(`  ✓ ${migration.name}`);
        });
      } else {
        this.logger.log('No migrations were executed');
      }
    } catch (error) {
      this.logger.error('Failed to run migrations', error);
      throw error;
    }
  }

  private async showMigrationStatus(): Promise<MigrationStatus> {
    try {
      const status = await this.getMigrationStatus();
      console.log(
        [
          `📋 Total migrations: ${status.totalMigrations}`,
          `✅ Executed: ${status.executed.length}`,
          `⏳ Pending: ${status.pending.length}`,
          `🔄 Last migration: ${status.lastMigration ?? 'None'}`,
        ].join('\n'),
      );

      return status;
    } catch (error) {
      this.logger.error('Failed to check migration status', error);
      throw error;
    }
  }

  private async getMigrationStatus(): Promise<MigrationStatus> {
    try {
      const allMigrations = this.dataSource.migrations;
      let executedMigrations: any[] = [];

      try {
        executedMigrations = await this.dataSource.query('SELECT * FROM migrations ORDER BY timestamp ASC');
      } catch {
        this.logger.debug('Migrations table does not exist yet');
      }

      const hasPendingMigrations = await this.dataSource.showMigrations();
      const executed = executedMigrations.map(m => m.name ?? `Migration_${m.timestamp}`);
      const allMigrationNames = allMigrations.map(m => m.name).filter((name): name is string => name !== undefined);
      const pending = hasPendingMigrations ? allMigrationNames.filter(name => !executed.includes(name)) : [];

      return {
        executed,
        pending,
        hasChanges: hasPendingMigrations,
        lastMigration: executed.at(-1),
        totalMigrations: allMigrations.length,
      };
    } catch (error) {
      this.logger.warn('Could not get detailed migration status', error);
      return {
        executed: [],
        pending: [],
        hasChanges: false,
        totalMigrations: 0,
      };
    }
  }
}
