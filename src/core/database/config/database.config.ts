import { registerAs } from '@nestjs/config';
import { TypeOrmModuleOptions } from '@nestjs/typeorm';
import { DataSource, DataSourceOptions } from 'typeorm';
import { join } from 'path';
import * as dotenv from 'dotenv';

dotenv.config();

console.log();

const databaseOptions: DataSourceOptions = {
  type: 'sqlite',
  database: process.env.DATABASE_PATH ?? join(process.cwd(), 'data', 'hungdong-pos.sqlite'),
  // entities: [join(__dirname, '../**/*.entity{.ts,.js}')],
  migrations: [join(__dirname, '../../../core/database/migrations/*{.ts,.js}')],
  synchronize: false,
  logging: ['error'],
  migrationsRun: false,
  migrationsTableName: 'migrations',
  extra: {
    busyTimeout: 30000,
    synchronous: 'NORMAL',
    cache_size: -64000,
    journal_mode: 'WAL',
    foreign_keys: true,
  },
};

// NestJS configuration
export const databaseConfig = registerAs('database', (): TypeOrmModuleOptions & { autoMigrate: boolean } => ({
  ...databaseOptions,
  autoLoadEntities: true,
  autoMigrate: process.env.DB_AUTO_MIGRATE !== 'false',
}));

// TypeORM CLI DataSource
const AppDataSource = new DataSource(databaseOptions);

export default AppDataSource;
