import { Global, Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';

import { databaseConfig } from './config/database.config';
import { MigrationRunnerService } from './migration-runner.service';

@Global()
@Module({
  imports: [
    ConfigModule.forFeature(databaseConfig),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => {
        const dbConfig = configService.get('database');

        if (!dbConfig) {
          throw new Error('Database config not found');
        }

        return dbConfig;
      },
      inject: [ConfigService],
    }),
  ],
  providers: [MigrationRunnerService],
})
export class DatabaseModule {}
