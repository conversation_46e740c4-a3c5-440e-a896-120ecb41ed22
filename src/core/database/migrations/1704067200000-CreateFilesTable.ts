import { MigrationInterface, QueryRunner, Table } from 'typeorm';

export class CreateFilesTable1704067200000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'files',
        columns: [
          {
            name: 'id',
            type: 'integer',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'originalName',
            type: 'varchar',
            length: '255',
            isNullable: false,
          },
          {
            name: 'filename',
            type: 'varchar',
            length: '255',
            isNullable: false,
            isUnique: true,
          },
          {
            name: 'mimeType',
            type: 'varchar',
            length: '100',
            isNullable: false,
          },
          {
            name: 'fileType',
            type: 'varchar',
            length: '20',
            isNullable: false,
          },
          {
            name: 'size',
            type: 'integer',
            isNullable: false,
          },
          {
            name: 'path',
            type: 'varchar',
            length: '500',
            isNullable: false,
          },
          {
            name: 'url',
            type: 'varchar',
            length: '500',
            isNullable: false,
          },
          {
            name: 'hash',
            type: 'varchar',
            length: '64',
            isNullable: true,
          },
          {
            name: 'status',
            type: 'varchar',
            length: '20',
            isNullable: false,
            default: "'uploading'",
          },
          {
            name: 'metadata',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'processedVariants',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'uploadedBy',
            type: 'integer',
            isNullable: true,
          },
          {
            name: 'alt',
            type: 'varchar',
            length: '100',
            isNullable: true,
          },
          {
            name: 'description',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'tags',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'isPublic',
            type: 'boolean',
            isNullable: false,
            default: false,
          },
          {
            name: 'lastAccessedAt',
            type: 'datetime',
            isNullable: true,
          },
          {
            name: 'downloadCount',
            type: 'integer',
            isNullable: false,
            default: 0,
          },
          {
            name: 'createdAt',
            type: 'datetime',
            isNullable: false,
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updatedAt',
            type: 'datetime',
            isNullable: false,
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'deletedAt',
            type: 'datetime',
            isNullable: true,
          },
        ],
      }),
      true,
    );

    await queryRunner.query('CREATE INDEX "IDX_FILE_ORIGINAL_NAME" ON "files" ("originalName")');
    await queryRunner.query('CREATE UNIQUE INDEX "IDX_FILE_FILENAME" ON "files" ("filename")');
    await queryRunner.query('CREATE INDEX "IDX_FILE_MIME_TYPE" ON "files" ("mimeType")');
    await queryRunner.query('CREATE INDEX "IDX_FILE_TYPE" ON "files" ("fileType")');
    await queryRunner.query('CREATE INDEX "IDX_FILE_HASH" ON "files" ("hash")');
    await queryRunner.query('CREATE INDEX "IDX_FILE_STATUS" ON "files" ("status")');
    await queryRunner.query('CREATE INDEX "IDX_FILE_UPLOADED_BY" ON "files" ("uploadedBy")');
    await queryRunner.query('CREATE INDEX "IDX_FILE_PUBLIC" ON "files" ("isPublic")');
    await queryRunner.query('CREATE INDEX "IDX_FILE_CREATED_AT" ON "files" ("createdAt")');
    await queryRunner.query('CREATE INDEX "IDX_FILE_ORIGINAL_NAME_DELETED" ON "files" ("originalName", "deletedAt")');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('DROP INDEX "IDX_FILE_ORIGINAL_NAME_DELETED"');
    await queryRunner.query('DROP INDEX "IDX_FILE_CREATED_AT"');
    await queryRunner.query('DROP INDEX "IDX_FILE_PUBLIC"');
    await queryRunner.query('DROP INDEX "IDX_FILE_UPLOADED_BY"');
    await queryRunner.query('DROP INDEX "IDX_FILE_STATUS"');
    await queryRunner.query('DROP INDEX "IDX_FILE_HASH"');
    await queryRunner.query('DROP INDEX "IDX_FILE_TYPE"');
    await queryRunner.query('DROP INDEX "IDX_FILE_MIME_TYPE"');
    await queryRunner.query('DROP INDEX "IDX_FILE_FILENAME"');
    await queryRunner.query('DROP INDEX "IDX_FILE_ORIGINAL_NAME"');

    await queryRunner.dropTable('files');
  }
}
