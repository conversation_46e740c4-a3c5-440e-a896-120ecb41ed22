import { MigrationInterface, QueryRunner, Table } from 'typeorm';

export class CreateOrdersTable1749717131460 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'orders',
        columns: [
          {
            name: 'id',
            type: 'integer',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'orderNumber',
            type: 'varchar',
            length: '20',
            isUnique: true,
          },
          {
            name: 'customerId',
            type: 'integer',
            isNullable: true,
          },
          {
            name: 'cashierId',
            type: 'integer',
          },
          {
            name: 'status',
            type: 'varchar',
            length: '20',
            default: "'pending'",
            comment: 'Order status: pending, completed, cancelled, refunded',
          },
          {
            name: 'subtotal',
            type: 'decimal',
            precision: 10,
            scale: 2,
            default: 0,
          },
          {
            name: 'taxAmount',
            type: 'decimal',
            precision: 10,
            scale: 2,
            default: 0,
          },
          {
            name: 'discountAmount',
            type: 'decimal',
            precision: 10,
            scale: 2,
            default: 0,
          },
          {
            name: 'grandTotal',
            type: 'decimal',
            precision: 10,
            scale: 2,
          },
          {
            name: 'paymentMethod',
            type: 'varchar',
            length: '20',
            default: "'cash'",
            comment: 'Payment method: cash, card, digital',
          },
          {
            name: 'notes',
            type: 'text',
            isNullable: true,
            comment: 'Order notes or special instructions',
          },
          {
            name: 'completedAt',
            type: 'datetime',
            isNullable: true,
          },
          {
            name: 'createdAt',
            type: 'datetime',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updatedAt',
            type: 'datetime',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'deletedAt',
            type: 'datetime',
            isNullable: true,
          },
        ],
        indices: [
          {
            name: 'IDX_ORDER_NUMBER',
            columnNames: ['orderNumber'],
            isUnique: true,
          },
          {
            name: 'IDX_ORDER_CUSTOMER',
            columnNames: ['customerId'],
          },
          {
            name: 'IDX_ORDER_CASHIER',
            columnNames: ['cashierId'],
          },
          {
            name: 'IDX_ORDER_STATUS',
            columnNames: ['status'],
          },
          {
            name: 'IDX_ORDER_CREATED_AT',
            columnNames: ['createdAt'],
          },
          {
            name: 'IDX_ORDER_COMPLETED_AT',
            columnNames: ['completedAt'],
          },
        ],
        foreignKeys: [
          {
            name: 'FK_ORDER_CASHIER',
            columnNames: ['cashierId'],
            referencedTableName: 'users',
            referencedColumnNames: ['id'],
            onDelete: 'RESTRICT',
            onUpdate: 'CASCADE',
          },
        ],
      }),
      true,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('orders');
  }
}
