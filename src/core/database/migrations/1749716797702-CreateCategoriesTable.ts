import { MigrationInterface, QueryRunner, Table } from 'typeorm';

export class CreateCategoriesTable1749716797702 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'categories',
        columns: [
          {
            name: 'id',
            type: 'integer',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'name',
            type: 'varchar',
            length: '100',
            isNullable: false,
          },
          {
            name: 'slug',
            type: 'varchar',
            length: '150',
            isUnique: true,
          },
          {
            name: 'description',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'imageUrl',
            type: 'varchar',
            length: '500',
            isNullable: true,
          },
          {
            name: 'isActive',
            type: 'boolean',
            default: true,
          },
          {
            name: 'sortOrder',
            type: 'integer',
            default: 0,
          },
          {
            name: 'parentId',
            type: 'uuid',
            isNullable: true,
          },
          {
            name: 'createdAt',
            type: 'datetime',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updatedAt',
            type: 'datetime',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'deletedAt',
            type: 'datetime',
            isNullable: true,
          },
        ],
        indices: [
          {
            name: 'IDX_CATEGORY_NAME',
            columnNames: ['name'],
          },
          {
            name: 'IDX_CATEGORY_SLUG',
            columnNames: ['slug'],
            isUnique: true,
          },
          {
            name: 'IDX_CATEGORY_ACTIVE',
            columnNames: ['isActive'],
          },
          {
            name: 'IDX_CATEGORY_SORT_ORDER',
            columnNames: ['sortOrder'],
          },
        ],
        checks: [
          {
            expression: '"name" <> \'\'',
          },
        ],
        foreignKeys: [
          {
            columnNames: ['parentId'],
            referencedColumnNames: ['id'],
            referencedTableName: 'categories',
            onDelete: 'SET NULL',
          },
        ],
      }),
      true,
    );

    // Create closure table manually for @Tree('closure-table')
    await queryRunner.createTable(
      new Table({
        name: 'categories_closure',
        columns: [
          {
            name: 'id_ancestor',
            type: 'uuid',
            isPrimary: true,
          },
          {
            name: 'id_descendant',
            type: 'uuid',
            isPrimary: true,
          },
        ],
        foreignKeys: [
          {
            columnNames: ['id_ancestor'],
            referencedTableName: 'categories',
            referencedColumnNames: ['id'],
            onDelete: 'CASCADE',
          },
          {
            columnNames: ['id_descendant'],
            referencedTableName: 'categories',
            referencedColumnNames: ['id'],
            onDelete: 'CASCADE',
          },
        ],
      }),
      true,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('category_closure');
    await queryRunner.dropTable('categories');
  }
}
