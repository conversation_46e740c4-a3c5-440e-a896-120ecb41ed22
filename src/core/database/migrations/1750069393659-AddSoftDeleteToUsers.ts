import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddSoftDeleteToUsers1750069393659 implements MigrationInterface {
  name = 'AddSoftDeleteToUsers1750069393659';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE \`users\`
      ADD COLUMN \`deletedAt\` datetime(6) NULL
    `);

    await queryRunner.query(`
      CREATE INDEX \`IDX_USER_DELETED_AT\` ON \`users\` (\`deletedAt\`)
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      DROP INDEX \`IDX_USER_DELETED_AT\` ON \`users\`
    `);

    await queryRunner.query(`
      ALTER TABLE \`users\`
      DROP COLUMN \`deletedAt\`
    `);
  }
}
