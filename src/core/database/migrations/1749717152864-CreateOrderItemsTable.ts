import { MigrationInterface, QueryRunner, Table } from 'typeorm';

export class CreateOrderItemsTable1749717152864 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'order_items',
        columns: [
          {
            name: 'id',
            type: 'integer',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'orderId',
            type: 'integer',
          },
          {
            name: 'productId',
            type: 'varchar',
            length: '36',
            generationStrategy: 'uuid',
          },
          {
            name: 'productName',
            type: 'varchar',
            length: '200',
          },
          {
            name: 'qty',
            type: 'integer',
          },
          {
            name: 'price',
            type: 'decimal',
            precision: 10,
            scale: 2,
          },
          {
            name: 'tax',
            type: 'integer',
          },
          {
            name: 'taxAmount',
            type: 'decimal',
            precision: 10,
            scale: 2,
          },
          {
            name: 'totalExclTax',
            type: 'decimal',
            precision: 15,
            scale: 4,
            comment: 'Total price (tax excluded)',
          },
          {
            name: 'totalInclTax',
            type: 'decimal',
            precision: 15,
            scale: 4,
            comment: 'Total price (tax included)',
          },
          {
            name: 'notes',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'createdAt',
            type: 'datetime',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updatedAt',
            type: 'datetime',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'deletedAt',
            type: 'datetime',
            isNullable: true,
          },
        ],
        indices: [
          {
            name: 'IDX_ORDER_ITEM_ORDER',
            columnNames: ['orderId'],
          },
          {
            name: 'IDX_ORDER_ITEM_PRODUCT',
            columnNames: ['productId'],
          },
          {
            name: 'IDX_ORDER_ITEM_SKU',
            columnNames: ['productSku'],
          },
        ],
        foreignKeys: [
          {
            name: 'FK_ORDER_ITEM_ORDER',
            columnNames: ['orderId'],
            referencedTableName: 'orders',
            referencedColumnNames: ['id'],
            onDelete: 'CASCADE',
            onUpdate: 'CASCADE',
          },
          {
            name: 'FK_ORDER_ITEM_PRODUCT',
            columnNames: ['productId'],
            referencedTableName: 'products',
            referencedColumnNames: ['id'],
            onDelete: 'RESTRICT',
            onUpdate: 'CASCADE',
          },
        ],
      }),
      true,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('order_items');
  }
}
