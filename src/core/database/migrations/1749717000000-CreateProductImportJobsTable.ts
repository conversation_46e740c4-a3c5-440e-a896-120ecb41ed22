import { MigrationInterface, QueryRunner, Table } from 'typeorm';

export class CreateProductImportJobsTable1749717000000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'product_import_jobs',
        columns: [
          {
            name: 'id',
            type: 'integer',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'version',
            type: 'varchar',
            length: '100',
            isUnique: true,
          },
          {
            name: 'fileName',
            type: 'varchar',
            length: '500',
          },
          {
            name: 'fileHash',
            type: 'varchar',
            length: '64',
          },
          {
            name: 'fileSize',
            type: 'bigint',
          },
          {
            name: 'status',
            type: 'varchar',
            length: '20',
            default: "'pending'",
          },
          {
            name: 'totalRecords',
            type: 'integer',
            default: 0,
          },
          {
            name: 'processedRecords',
            type: 'integer',
            default: 0,
          },
          {
            name: 'insertedRecords',
            type: 'integer',
            default: 0,
          },
          {
            name: 'updatedRecords',
            type: 'integer',
            default: 0,
          },
          {
            name: 'skippedRecords',
            type: 'integer',
            default: 0,
          },
          {
            name: 'errorRecords',
            type: 'integer',
            default: 0,
          },
          {
            name: 'batchSize',
            type: 'integer',
            default: 1000,
          },
          {
            name: 'startedAt',
            type: 'datetime',
            isNullable: true,
          },
          {
            name: 'completedAt',
            type: 'datetime',
            isNullable: true,
          },
          {
            name: 'durationMs',
            type: 'integer',
            isNullable: true,
          },
          {
            name: 'errorMessage',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'errorDetails',
            type: 'json',
            isNullable: true,
          },
          {
            name: 'notes',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'createdAt',
            type: 'datetime',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updatedAt',
            type: 'datetime',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'deletedAt',
            type: 'datetime',
            isNullable: true,
          },
        ],
        indices: [
          {
            name: 'IDX_MIGRATION_VERSION',
            columnNames: ['version'],
          },
          {
            name: 'IDX_MIGRATION_FILE_HASH',
            columnNames: ['fileHash'],
          },
          {
            name: 'IDX_MIGRATION_STATUS',
            columnNames: ['status'],
          },
          {
            name: 'IDX_MIGRATION_STATUS_CREATED',
            columnNames: ['status', 'createdAt'],
          },
        ],
      }),
      true,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('import_histories');
  }
}
