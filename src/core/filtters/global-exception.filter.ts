import { ExceptionFilter, Catch, ArgumentsHost, HttpException, Logger, Injectable } from '@nestjs/common';
import { Request, Response } from 'express';
import { I18nContext } from 'nestjs-i18n';
import { v4 as uuidv4 } from 'uuid';

import { BaseError } from '../errors/base.error';
import { ErrorFactory } from '../errors/error.factory';
import { TranslationService } from '@/common/services/translation.service';

export interface ErrorResponse {
  success: false;
  error: {
    message: string;
    details?: any[];
    path: string;
    method: string;
    timestamp: string;
  };
}

@Injectable()
@Catch()
export class GlobalExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(GlobalExceptionFilter.name);

  constructor(
    private readonly translationService: TranslationService,
    private readonly errorFactory: ErrorFactory,
  ) {}

  catch(exception: unknown, host: ArgumentsHost): void {
    const ctx = host.switchToHttp();
    const res = ctx.getResponse<Response>();
    const req = ctx.getRequest<Request>();
    const i18n = I18nContext.current(host);

    let error = this.normalizeToBaseError(exception);
    const localizedMessage = this.translationService.translateError(error.message, i18n, error.context);

    const errorResponse: ErrorResponse = {
      success: false,
      error: {
        message: localizedMessage,
        path: req.url,
        method: req.method,
        timestamp: new Date().toISOString(),
      },
    };

    this.logger.warn(`${error.message}`, {
      request: {
        method: req.method,
        url: req.url,
        exception,
      },
    });

    res.setHeader('X-Correlation-ID', uuidv4() ?? 'unknown');
    res.setHeader('Content-Type', 'application/json');
    res.status(error.httpStatus).json(errorResponse);
  }

  private normalizeToBaseError(exception: unknown): BaseError {
    if (exception instanceof BaseError) {
      return exception;
    }

    if (exception instanceof HttpException) {
      const status = exception.getStatus();
      return this.errorFactory.mapHttpError(status);
    }

    if (exception instanceof Error) {
      return this.errorFactory.mapUnexpectedError(exception);
    }

    return this.errorFactory.mapUnexpectedError(new Error('Unknown exception thrown'));
  }
}
