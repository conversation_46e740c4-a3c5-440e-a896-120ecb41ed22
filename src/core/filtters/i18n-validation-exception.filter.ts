import { ArgumentsHost, Catch, ExceptionFilter, Injectable, Optional, ValidationError } from '@nestjs/common';
import { I18nContext, I18nService, I18nValidationException } from 'nestjs-i18n';
import { Request, Response } from 'express';
import { TranslationService } from '@/common/services/translation.service';

export interface ErrorResponse {
  success: false;
  error: {
    message: string;
    details?: string[] | string;
    path: string;
    method: string;
    timestamp: string;
  };
}

@Injectable()
@Catch(I18nValidationException)
export class I18nValidationExceptionFilters implements ExceptionFilter {
  constructor(
    @Optional()
    private readonly translationService: TranslationService,
    @Optional()
    private readonly i18nService: I18nService,
  ) {}

  async catch(exception: I18nValidationException, host: ArgumentsHost): Promise<void> {
    const ctx = host.switchToHttp();
    const res = ctx.getResponse<Response>();
    const req = ctx.getRequest<Request>();
    const i18nContext = I18nContext.current(host);
    const lang = this.translationService.getCurrentLanguage(i18nContext);

    const formattedErrors = await this.formatErrors(exception.errors ?? [], lang);
    const translatedMessage = this.i18nService.translate('validation.invalid', {
      lang,
      defaultValue: 'Invalid data provided',
    });

    const errorResponse: ErrorResponse = {
      success: false,
      error: {
        message: translatedMessage,
        details: formattedErrors.length === 1 ? formattedErrors[0] : formattedErrors,
        path: req.url,
        method: req.method,
        timestamp: new Date().toISOString(),
      },
    };

    res.status(exception.getStatus()).json(errorResponse);
  }

  private async formatErrors(errors: ValidationError[], lang: string): Promise<string[]> {
    const messages: string[] = [];

    for (const error of errors) {
      if (error.constraints) {
        for (const [key, rawMessage] of Object.entries(error.constraints)) {
          messages.push(await this.translateMessage(rawMessage, error, key, lang));
        }
      }

      if (error.children?.length) {
        messages.push(...(await this.formatErrors(error.children, lang)));
      }
    }

    return messages;
  }

  private async translateMessage(
    rawMessage: string,
    error: ValidationError,
    constraintKey: string,
    lang: string,
  ): Promise<string> {
    try {
      if (rawMessage.includes('|')) {
        const [i18nKey, jsonArgs] = rawMessage.split('|');
        let args = {};

        try {
          args = JSON.parse(jsonArgs || '{}');
        } catch {
          args = {};
        }

        return await this.i18nService.translate(i18nKey, {
          lang,
          args,
        });
      }

      if (this.i18nService && this.isI18nKey(rawMessage)) {
        try {
          return await this.i18nService.translate(rawMessage, {
            lang,
            args: {
              property: error.property,
              value: error.value,
            },
          });
        } catch {
          return this.getFallbackMessage(constraintKey, error.property);
        }
      }

      return rawMessage;
    } catch {
      return this.getFallbackMessage(constraintKey, error.property);
    }
  }

  private isI18nKey(message: string): boolean {
    return /^[a-zA-Z][a-zA-Z0-9_]*(\.[a-zA-Z][a-zA-Z0-9_]*)*$/.test(message);
  }

  private getFallbackMessage(constraintKey: string, field: any): string {
    return `${field} ${constraintKey}`;
  }
}
