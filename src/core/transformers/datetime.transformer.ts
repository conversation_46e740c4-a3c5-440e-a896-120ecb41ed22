import { ValueTransformer } from 'typeorm';
import * as moment from 'moment';
import { Moment } from 'moment';

export class DateTimeTransformer implements ValueTransformer {
  private isNullOrUndefined<T>(obj: T | null | undefined): obj is null | undefined {
    return typeof obj === 'undefined' || obj === null;
  }

  public from(value?: number | string | Date | null): Moment | string {
    if (this.isNullOrUndefined(value)) {
      return '';
    }

    return value ? moment(value).format('YYYY-MM-DD HH:mm:ss') : moment();
  }

  public to(value?: string): string | undefined {
    return value;
  }
}
