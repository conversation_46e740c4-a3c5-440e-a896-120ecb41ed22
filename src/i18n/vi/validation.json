{"invalid": "<PERSON><PERSON> li<PERSON><PERSON> không hợp lệ", "common": {"required": "{field} l<PERSON> b<PERSON><PERSON> bu<PERSON>c", "invalid": "{field} kh<PERSON><PERSON> hợp lệ", "tooLong": "{field} quá dài (tối đa {max} ký tự)", "tooShort": "{field} qu<PERSON> (t<PERSON><PERSON> thiểu {min} ký tự)", "invalidFormat": "{field} c<PERSON> đ<PERSON>nh dạng không hợp lệ", "mustBeString": "{field} ph<PERSON>i là chuỗi ký tự", "mustBeNumber": "{field} ph<PERSON>i là số", "mustBeBoolean": "{field} ph<PERSON>i là giá trị boolean", "mustBeArray": "{field} ph<PERSON><PERSON> là mảng", "mustBeObject": "{field} ph<PERSON>i là đối tượng", "mustBeUuid": "{field} ph<PERSON>i là UUID hợp lệ", "mustBeEmail": "{field} ph<PERSON>i là địa chỉ email hợp lệ", "mustBeUrl": "{field} phải là URL hợp lệ", "mustBeDate": "{field} ph<PERSON><PERSON> là ng<PERSON>y hợp lệ", "mustBePositive": "{field} ph<PERSON>i là số dương", "mustBeNegative": "{field} ph<PERSON>i là số âm", "mustBeInteger": "{field} ph<PERSON>i là số nguyên", "mustBeDecimal": "{field} ph<PERSON><PERSON> là số thập phân", "mustBeInRange": "{field} phải nằm trong khoảng {min} đến {max}", "mustBeOneOf": "{field} phải là một trong: {values}", "mustNotBeEmpty": "{field} kh<PERSON><PERSON> đ<PERSON><PERSON><PERSON> để trống", "mustBeUnique": "{field} ph<PERSON>i là duy nhất", "invalidPattern": "{field} không khớp với mẫu yêu cầu", "invalidLength": "{field} ph<PERSON>i có chính xác {length} ký tự"}, "fields": {"email": "Email", "password": "<PERSON><PERSON><PERSON>", "username": "<PERSON><PERSON><PERSON> đ<PERSON>p", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Họ", "phone": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "address": "Địa chỉ", "city": "<PERSON><PERSON><PERSON><PERSON> phố", "country": "Quốc gia", "zipCode": "<PERSON><PERSON> b<PERSON>u đi<PERSON>n", "name": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON>", "price": "Giá", "quantity": "Số lượng", "sku": "Mã SKU", "barcode": "Mã vạch", "category": "<PERSON><PERSON>", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "type": "<PERSON><PERSON><PERSON>", "role": "<PERSON>ai trò", "permissions": "<PERSON><PERSON><PERSON><PERSON>", "id": "ID", "createdAt": "<PERSON><PERSON><PERSON>", "updatedAt": "<PERSON><PERSON><PERSON> c<PERSON>", "deletedAt": "<PERSON><PERSON><PERSON>"}, "specific": {"password": {"tooWeak": "<PERSON><PERSON>t khẩu phải chứa ít nhất một chữ hoa, một chữ thường, một số và một ký tự đặc biệt", "minLength": "<PERSON><PERSON>t khẩu phải có ít nhất {min} ký tự", "maxLength": "<PERSON><PERSON><PERSON> khẩu không đ<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá {max} ký tự"}, "email": {"invalid": "<PERSON><PERSON> lòng nhập địa chỉ email hợp lệ", "alreadyExists": "Địa chỉ email này đã được đăng ký"}, "username": {"invalid": "<PERSON><PERSON><PERSON> đ<PERSON>ng nhập chỉ có thể chứa chữ cái, số và dấu gạch dưới", "minLength": "<PERSON><PERSON><PERSON> đ<PERSON>ng nhập phải có ít nhất {min} ký tự", "maxLength": "<PERSON>ê<PERSON> đăng nhập không đ<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá {max} ký tự", "alreadyExists": "<PERSON><PERSON><PERSON> đăng nhập này đã đư<PERSON>c sử dụng"}, "phone": {"invalid": "<PERSON><PERSON> lòng nhập số điện tho<PERSON>i hợp lệ", "format": "<PERSON><PERSON> điện thoại phải có định dạng: +1234567890"}, "category": {"name": {"minLength": "<PERSON>ên danh mục phải có ít nhất {min} ký tự", "maxLength": "<PERSON><PERSON><PERSON> danh mục không đ<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá {max} ký tự"}, "description": {"maxLength": "<PERSON><PERSON> tả danh mục không đ<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá {max} ký tự"}}}}