{"auth": {"INVALID_CREDENTIALS": "Invalid username or password", "USER_NOT_FOUND": "User '{username}' not found", "USER_ALREADY_EXISTS": "User already exists", "USERNAME_ALREADY_EXISTS": "Username '{username}' is already taken", "REGISTRATION_FAILED": "Registration failed {reason}", "USER_CREATION_FAILED": "Failed to create user account", "UNAUTHORIZED": "Unauthorized access", "TOKEN_EXPIRED": "Token has expired", "INVALID_TOKEN": "Invalid token", "INSUFFICIENT_PERMISSIONS": "Insufficient permissions", "ACCOUNT_DISABLED": "Account is disabled", "PASSWORD_RESET_REQUIRED": "Password reset is required", "INVALID_REFRESH_TOKEN": "Invalid refresh token", "LOGIN_SUCCESS": "Login successful", "LOGOUT_SUCCESS": "Logout successful", "REGISTRATION_SUCCESS": "User registered successfully", "ACCOUNT_DEACTIVATED": "User account is deactivated"}, "user": {"USERNAME_ALREADY_EXISTS": "Username '{username}' is already taken"}, "validation": {"VALIDATION_ERROR": "Validation error", "INVALID_INPUT": "Invalid input provided", "MISSING_REQUIRED_FIELD": "Required field is missing", "INVALID_EMAIL_FORMAT": "Invalid email format", "INVALID_PASSWORD_FORMAT": "Invalid password format", "INVALID_USERNAME_FORMAT": "Invalid username format", "INVALID_PHONE_FORMAT": "Invalid phone number format", "INVALID_DATE_FORMAT": "Invalid date format", "INVALID_ENUM_VALUE": "Invalid enum value", "FIELD_TOO_LONG": "Field value is too long", "FIELD_TOO_SHORT": "Field value is too short"}, "database": {"DATABASE_CONNECTION_ERROR": "Database connection error", "DATABASE_QUERY_ERROR": "Database query error", "DUPLICATE_ENTRY": "Duplicate entry", "FOREIGN_KEY_CONSTRAINT": "Foreign key constraint violation", "TRANSACTION_FAILED": "Transaction failed", "RECORD_NOT_FOUND": "Record not found", "CONCURRENT_UPDATE_ERROR": "Concurrent update error", "DATABASE_TIMEOUT": "Database operation timeout"}, "business": {"BUSINESS_RULE_VIOLATION": "Business rule violation", "INSUFFICIENT_BALANCE": "Insufficient balance", "OPERATION_NOT_ALLOWED": "Operation not allowed", "BUSINESS_RESOURCE_LOCKED": "Resource is locked", "QUOTA_EXCEEDED": "<PERSON><PERSON><PERSON> exceeded", "INVALID_STATE_TRANSITION": "Invalid state transition", "DEPENDENCY_CONFLICT": "Dependency conflict"}, "system": {"INTERNAL_SERVER_ERROR": "Internal server error", "SERVICE_UNAVAILABLE": "Service unavailable", "EXTERNAL_SERVICE_ERROR": "External service error", "CONFIGURATION_ERROR": "Configuration error", "FILE_SYSTEM_ERROR": "File system error", "NETWORK_ERROR": "Network error", "TIMEOUT_ERROR": "Request timeout", "RATE_LIMIT_EXCEEDED": "Rate limit exceeded", "URL_NOT_FOUND": "URL not found", "TYPE_ERROR": "Type error", "REFERENCE_ERROR": "Reference error", "SYNTAX_ERROR": "Syntax error"}, "resource": {"RESOURCE_NOT_FOUND": "Resource not found", "RESOURCE_ALREADY_EXISTS": "Resource already exists", "RESOURCE_ACCESS_DENIED": "Resource access denied", "RESOURCE_LOCKED": "Resource is locked", "RESOURCE_EXPIRED": "Resource has expired", "RESOURCE_CORRUPTED": "Resource is corrupted"}, "product": {"PRODUCT_NOT_FOUND": "Product not found", "PRODUCT_ALREADY_EXISTS": "Product already exists", "INVALID_PRICE": "Invalid price", "INVALID_STOCK_QUANTITY": "Invalid stock quantity", "LOW_STOCK_ALERT": "Low stock alert", "OUT_OF_STOCK": "Out of stock", "PRODUCT_DISCONTINUED": "Product discontinued", "INVALID_PRODUCT_STATUS": "Invalid product status", "BULK_OPERATION_FAILED": "Bulk operation failed"}, "upload": {"FILE_TOO_LARGE": "File size exceeds maximum allowed size of {maxSize}", "INVALID_FILE_TYPE": "File type '{fileType}' is not allowed. Allowed types: {allowedTypes}", "TOO_MANY_FILES": "Too many files uploaded. Maximum allowed: {maxFiles}", "INVALID_FILENAME": "Invalid filename format", "UPLOAD_FAILED": "File upload failed", "PROCESSING_FAILED": "Image processing failed", "VIRUS_DETECTED": "Virus detected in uploaded file", "FILE_NOT_FOUND": "File not found", "FILE_ALREADY_EXISTS": "File already exists", "UNSUPPORTED_IMAGE_FORMAT": "Unsupported image format '{format}'", "IMAGE_PROCESSING_ERROR": "Image processing error: {error}", "INSUFFICIENT_STORAGE_SPACE": "Insufficient storage space available", "FILE_CORRUPTED": "File appears to be corrupted", "UPLOAD_TIMEOUT": "Upload timeout exceeded", "STORAGE_INIT_ERROR": "Failed to initialize storage: {message}", "STORAGE_ERROR": "Storage error occurred for file {originalName}"}}