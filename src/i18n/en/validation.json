{"invalid": "Invalid data provided", "common": {"required": "{field} is required", "invalid": "{field} is invalid", "tooLong": "{field} is too long (maximum {max} characters)", "tooShort": "{field} is too short (minimum {min} characters)", "invalidFormat": "{field} has invalid format", "mustBeString": "{field} must be a string", "mustBeNumber": "{field} must be a number", "mustBeBoolean": "{field} must be a boolean", "mustBeArray": "{field} must be an array", "mustBeObject": "{field} must be an object", "mustBeUuid": "{field} must be a valid UUID", "mustBeEmail": "{field} must be a valid email address", "mustBeUrl": "{field} must be a valid URL", "mustBeDate": "{field} must be a valid date", "mustBePositive": "{field} must be a positive number", "mustBeNegative": "{field} must be a negative number", "mustBeInteger": "{field} must be an integer", "mustBeDecimal": "{field} must be a decimal number", "mustBeInRange": "{field} must be between {min} and {max}", "mustBeOneOf": "{field} must be one of: {values}", "mustNotBeEmpty": "{field} must not be empty", "mustBeUnique": "{field} must be unique", "invalidPattern": "{field} does not match the required pattern", "invalidLength": "{field} must be exactly {length} characters long"}, "fields": {"email": "Email", "password": "Password", "username": "Username", "firstName": "First name", "lastName": "Last name", "phone": "Phone number", "address": "Address", "city": "City", "country": "Country", "zipCode": "ZIP code", "name": "Name", "description": "Description", "price": "Price", "quantity": "Quantity", "sku": "SKU", "barcode": "Barcode", "category": "Category", "status": "Status", "type": "Type", "role": "Role", "permissions": "Permissions", "id": "ID", "createdAt": "Created at", "updatedAt": "Updated at", "deletedAt": "Deleted at"}, "specific": {"password": {"tooWeak": "Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character", "minLength": "Password must be at least {min} characters long", "maxLength": "Password must not exceed {max} characters"}, "email": {"invalid": "Please enter a valid email address", "alreadyExists": "This email address is already registered"}, "username": {"invalid": "Username can only contain letters, numbers, and underscores", "minLength": "Username must be at least {min} characters long", "maxLength": "Username must not exceed {max} characters", "alreadyExists": "This username is already taken"}, "phone": {"invalid": "Please enter a valid phone number", "format": "Phone number must be in format: +1234567890"}, "category": {"name": {"minLength": "Category name must be at least {min} characters long", "maxLength": "Category name must not exceed {max} characters"}, "description": {"maxLength": "Category description must not exceed {max} characters"}}}}