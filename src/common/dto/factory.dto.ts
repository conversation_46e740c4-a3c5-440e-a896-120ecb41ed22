// import { PartialType, OmitType } from '@nestjs/swagger';
// import { BaseDto } from './base.dto';

// export function createDtoFactory<T extends BaseDto>(BaseDtoClass: new () => T) {
//   abstract class CreateDto extends OmitType(BaseDtoClass, [
//     'id',
//     'createdAt',
//     'updatedAt',
//     'deletedAt',
//   ] as const) {}
//   return CreateDto;
// }

// export function updateDtoFactory<T extends BaseDto>(
//   CreateDtoClass: abstract new (...args: any[]) => T,
// ) {
//   abstract class UpdateDto extends PartialType(CreateDtoClass) {}
//   return UpdateDto;
// }
