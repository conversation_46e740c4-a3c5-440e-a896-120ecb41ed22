// import { PartialType, OmitType } from '@nestjs/swagger';
// import { BaseEntity } from '@/common/base/base.entity';

// export function createDtoFactory<T extends BaseEntity>(Base: new () => T) {
//   return class CreateDto extends OmitType(Base, ['id', 'createdAt', 'updatedAt', 'deletedAt'] as const) {};
// }

// export function updateDtoFactory<T extends BaseEntity>(CreateDto: new () => T) {
//   return class UpdateDto extends PartialType(CreateDto) {};
// }

export abstract class BaseDto {
  id: string;
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date;
}
