import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { PaginationMetaDto } from './pagination.dto';

export class BaseResponse<T> {
  @ApiProperty({
    description: 'Response status',
    example: true,
  })
  status: boolean;

  @ApiProperty({
    description: 'Response data',
  })
  data?: T;

  @ApiPropertyOptional({
    description: 'Pagination metadata (only for paginated responses)',
    type: PaginationMetaDto,
  })
  meta?: PaginationMetaDto;

  private constructor(status: boolean, data?: T, meta?: PaginationMetaDto) {
    this.status = status;
    this.data = data;
    this.meta = meta;
  }

  static success<T>(data: T, meta?: PaginationMetaDto): BaseResponse<T> {
    return new BaseResponse<T>(true, data, meta);
  }

  // Check if a value is already wrapped in BaseResponse
  static isWrapped(value: unknown): boolean {
    return (
      value !== null &&
      value !== undefined &&
      typeof value === 'object' &&
      'status' in value &&
      typeof value.status === 'boolean'
    );
  }
}
