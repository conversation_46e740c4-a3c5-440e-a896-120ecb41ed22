import { PaginationMetaDto } from './pagination.dto';

export class PaginatedResult<T> {
  data: T[];
  meta: PaginationMetaDto;

  constructor(data: T[], total: number, page: number, limit: number) {
    this.data = data;
    this.meta = new PaginationMetaDto(total, page, limit);
  }

  // Check if a value is a paginated result
  static isPaginatedResult(value: unknown): value is PaginatedResult<unknown> {
    if (!value || typeof value !== 'object' || !('data' in value) || !('meta' in value)) {
      return false;
    }

    const obj = value as Record<string, unknown>;
    const meta = obj.meta;

    return (
      Array.isArray(obj.data) &&
      meta !== null &&
      meta !== undefined &&
      typeof meta === 'object' &&
      'total' in meta &&
      'page' in meta &&
      'limit' in meta
    );
  }
}
