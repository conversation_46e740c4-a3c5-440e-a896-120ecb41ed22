import { applyDecorators, HttpStatus } from '@nestjs/common';
import {
  ApiQuery,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiNotFoundResponse,
  ApiBadRequestResponse,
} from '@nestjs/swagger';

export function PaginationDoc(): MethodDecorator {
  return applyDecorators(
    ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number' }),
    ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page' }),
    ApiQuery({ name: 'active', required: false, type: Boolean, description: 'Filter by active status' }),
  );
}

export function CreateEntityDoc(): MethodDecorator {
  return applyDecorators(
    ApiOperation({ summary: 'Create a new entity' }),
    ApiResponse({
      status: HttpStatus.CREATED,
      description: 'Entity created successfully',
    }),
    ApiBadRequestResponse({ description: 'Validation error' }),
  );
}

export function GetAllEntitiesDoc(): MethodDecorator {
  return applyDecorators(
    ApiOperation({ summary: 'Get all entities' }),
    ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number' }),
    ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page' }),
    ApiResponse({
      status: HttpStatus.OK,
      description: 'Entities retrieved successfully',
    }),
  );
}

export function GetEntityByIdDoc(): MethodDecorator {
  return applyDecorators(
    ApiOperation({ summary: 'Get entity by ID' }),
    ApiParam({ name: 'id', description: 'Entity ID', type: String }),
    ApiResponse({
      status: HttpStatus.OK,
      description: 'Entity retrieved successfully',
    }),
    ApiNotFoundResponse({ description: 'Entity not found' }),
  );
}

export function UpdateEntityDoc(): MethodDecorator {
  return applyDecorators(
    ApiOperation({ summary: 'Update entity' }),
    ApiParam({ name: 'id', description: 'Entity ID', type: String }),
    ApiResponse({
      status: HttpStatus.OK,
      description: 'Entity updated successfully',
    }),
    ApiNotFoundResponse({ description: 'Entity not found' }),
  );
}

export function DeleteEntityDoc(): MethodDecorator {
  return applyDecorators(
    ApiOperation({
      summary: 'Soft delete entity',
      description:
        'Marks the entity as deleted without permanently removing it from the database. ' +
        'The entity can be restored later.',
    }),
    ApiParam({ name: 'id', description: 'Entity ID', type: String }),
    ApiResponse({
      status: HttpStatus.NO_CONTENT,
      description: 'Entity soft deleted successfully',
    }),
    ApiNotFoundResponse({ description: 'Entity not found' }),
  );
}

export function SoftDeleteEntityDoc(): MethodDecorator {
  return applyDecorators(
    ApiOperation({
      summary: 'Soft delete entity (explicit)',
      description:
        'Explicitly marks the entity as deleted and returns the soft-deleted entity. ' +
        'This is an alternative to the standard DELETE endpoint.',
    }),
    ApiParam({ name: 'id', description: 'Entity ID', type: String }),
    ApiResponse({
      status: HttpStatus.OK,
      description: 'Entity soft deleted successfully and returned',
    }),
    ApiNotFoundResponse({ description: 'Entity not found' }),
  );
}

export function RestoreEntityDoc(): MethodDecorator {
  return applyDecorators(
    ApiOperation({ summary: 'Restore entity' }),
    ApiParam({ name: 'id', description: 'Entity ID', type: String }),
    ApiResponse({
      status: HttpStatus.OK,
      description: 'Entity restored successfully',
    }),
    ApiNotFoundResponse({ description: 'Entity not found' }),
  );
}
