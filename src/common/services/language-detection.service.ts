import { Injectable } from '@nestjs/common';
import { I18nContext } from 'nestjs-i18n';

export type SupportedLanguage = 'en' | 'vi';

@Injectable()
export class LanguageDetectionService {
  private readonly supportedLanguages: SupportedLanguage[] = ['en', 'vi'];
  private readonly defaultLanguage: SupportedLanguage = 'en';

  detectLanguage(i18nContext?: I18nContext): SupportedLanguage {
    if (!i18nContext) {
      return this.defaultLanguage;
    }

    const detectedLang = i18nContext.lang;

    if (this.isSupportedLanguage(detectedLang)) {
      return detectedLang;
    }

    // Try to extract language code from locale (e.g., 'en-US' -> 'en')
    const langCode = detectedLang?.split('-')[0];
    if (langCode && this.isSupportedLanguage(langCode)) {
      return langCode;
    }

    return this.defaultLanguage;
  }

  detectLanguageFromHeader(acceptLanguageHeader?: string): SupportedLanguage {
    if (!acceptLanguageHeader) {
      return this.defaultLanguage;
    }

    // Parse Accept-Language header (e.g., "en-US,en;q=0.9,vi;q=0.8")
    const languages = this.parseAcceptLanguageHeader(acceptLanguageHeader);

    for (const lang of languages) {
      if (this.isSupportedLanguage(lang.code)) {
        return lang.code;
      }

      // Try language without region (e.g., 'en-US' -> 'en')
      const langCode = lang.code.split('-')[0];
      if (this.isSupportedLanguage(langCode)) {
        return langCode;
      }
    }

    return this.defaultLanguage;
  }

  getSupportedLanguages(): SupportedLanguage[] {
    return [...this.supportedLanguages];
  }

  getDefaultLanguage(): SupportedLanguage {
    return this.defaultLanguage;
  }

  isSupportedLanguage(lang: string): lang is SupportedLanguage {
    return this.supportedLanguages.includes(lang as SupportedLanguage);
  }

  private parseAcceptLanguageHeader(header: string): Array<{ code: string; quality: number }> {
    const languages = header
      .split(',')
      .map(lang => {
        const trimmedLang = lang.trim();
        const [code, qPart] = trimmedLang.split(';');

        let quality = 1.0;
        if (qPart) {
          const qValue = qPart.trim().replace(/^q\s*=\s*/, '');
          const parsedQuality = parseFloat(qValue);
          if (!isNaN(parsedQuality)) {
            quality = parsedQuality;
          }
        }

        return {
          code: code.trim(),
          quality,
        };
      })
      .filter(lang => lang.code && !isNaN(lang.quality))
      .sort((a, b) => b.quality - a.quality);

    return languages;
  }
}
