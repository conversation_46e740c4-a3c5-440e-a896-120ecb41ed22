import { Injectable } from '@nestjs/common';
import { I18nService, I18nContext } from 'nestjs-i18n';
import { LanguageDetectionService, SupportedLanguage } from './language-detection.service';

@Injectable()
export class TranslationService {
  constructor(
    private readonly i18nService: I18nService,
    private readonly languageDetectionService: LanguageDetectionService,
  ) {}

  translateError(key: string, i18nContext?: I18nContext, interpolationData?: Record<string, any>): string {
    const language = this.getCurrentLanguage(i18nContext);
    return this.translateErrorWithLanguage(key, language, interpolationData);
  }

  translateValidation(key: string, i18nContext?: I18nContext, interpolationData?: Record<string, any>): string {
    const language = this.getCurrentLanguage(i18nContext);
    return this.translateValidationWithLanguage(key, language, interpolationData);
  }

  translateKey(key: string, i18nContext?: I18nContext, interpolationData?: Record<string, any>): string {
    const language = this.getCurrentLanguage(i18nContext);
    try {
      return this.i18nService.translate(key, {
        lang: language,
        args: interpolationData,
      });
    } catch {
      if (language !== 'en') {
        try {
          return this.i18nService.translate(key, {
            lang: 'en',
            args: interpolationData,
          });
        } catch {
          return key;
        }
      }
      return key;
    }
  }

  getCurrentLanguage(i18nContext?: I18nContext): SupportedLanguage {
    return this.languageDetectionService.detectLanguage(i18nContext);
  }

  private translateErrorWithLanguage(
    key: string,
    language: SupportedLanguage,
    interpolationData?: Record<string, any>,
  ): string {
    const translationKey = key.startsWith('errors.') ? key : `errors.${key}`;

    try {
      return this.i18nService.translate(translationKey, {
        lang: language,
        args: interpolationData,
      });
    } catch {
      if (language !== 'en') {
        try {
          return this.i18nService.translate(translationKey, {
            lang: 'en',
            args: interpolationData,
          });
        } catch {
          return key;
        }
      }
      return key;
    }
  }

  private translateValidationWithLanguage(
    key: string,
    language: SupportedLanguage,
    interpolationData?: Record<string, any>,
  ): string {
    const translationKey = key.startsWith('validation.') ? key : `validation.${key}`;

    try {
      return this.i18nService.translate(translationKey, {
        lang: language,
        args: interpolationData,
      });
    } catch {
      if (language !== 'en') {
        try {
          return this.i18nService.translate(translationKey, {
            lang: 'en',
            args: interpolationData,
          });
        } catch {
          return key;
        }
      }
      return key;
    }
  }
}
