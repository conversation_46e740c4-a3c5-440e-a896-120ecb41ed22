export const PRODUCT_CONSTANTS = {
  DEFAULT_PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 100,
  MIN_STOCK_LEVEL: 0,
  MAX_STOCK_LEVEL: 999999,
  MIN_PRICE: 0,
  MAX_PRICE: 999999.99,
  MIN_COST: 0,
  MAX_COST: 999999.99,
  SKU_MAX_LENGTH: 50,
  BARCODE_MAX_LENGTH: 50,
  NAME_MAX_LENGTH: 255,
  DESCRIPTION_MAX_LENGTH: 1000,
  CATEGORY_MAX_LENGTH: 100,
  BRAND_MAX_LENGTH: 100,
  UNIT_MAX_LENGTH: 50,
  TAG_MAX_LENGTH: 50,
  MAX_TAGS: 10,
  MAX_IMAGES: 10,
  IMAGE_URL_MAX_LENGTH: 500,
} as const;

export const PRODUCT_MESSAGES = {
  PRODUCT_NOT_FOUND: 'Product not found',
  PRODUCT_ALREADY_EXISTS: 'Product with this SKU already exists',
  INVALID_SKU: 'Invalid SKU format',
  INVALID_BARCODE: 'Invalid barcode format',
  INVALID_PRICE: 'Price must be greater than or equal to 0',
  INVALID_COST: 'Cost must be greater than or equal to 0',
  INVALID_STOCK: 'Stock quantity must be greater than or equal to 0',
  INVALID_MIN_STOCK: 'Minimum stock level must be greater than or equal to 0',
  LOW_STOCK_ALERT: 'Product stock is below minimum level',
  OUT_OF_STOCK: 'Product is out of stock',
  PRODUCT_CREATED: 'Product created successfully',
  PRODUCT_UPDATED: 'Product updated successfully',
  PRODUCT_DELETED: 'Product deleted successfully',
  BULK_OPERATION_SUCCESS: 'Bulk operation completed successfully',
  BULK_OPERATION_PARTIAL: 'Bulk operation completed with some errors',
} as const;

export const PRODUCT_SEARCH_FIELDS = ['name', 'description', 'sku', 'barcode', 'category', 'brand', 'tags'] as const;

export const PRODUCT_SORT_FIELDS = ['name', 'price', 'cost', 'stockQuantity', 'createdAt', 'updatedAt'] as const;

export const PRODUCT_VALIDATION_PATTERNS = {
  SKU: /^[A-Z0-9\-_]+$/,
  BARCODE: /^[0-9]+$/,
  PRICE: /^\d+(\.\d{1,2})?$/,
  UNIT: /^[a-zA-Z\s]+$/,
} as const;
