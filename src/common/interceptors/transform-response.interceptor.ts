import { Injectable, NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { ClassConstructor, plainToInstance } from 'class-transformer';

import { BaseResponse } from '../dto/base-response.dto';
import { PaginatedResult } from '../dto/paginated-result.dto';

@Injectable()
export class TransformResponseInterceptor<T> implements NestInterceptor<T, any> {
  constructor(private readonly dto: ClassConstructor<T>) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    return next.handle().pipe(
      map(data => {
        if (data == null) return data;

        if (BaseResponse.isWrapped(data)) return data;

        if (PaginatedResult.isPaginatedResult(data)) {
          const transformedData = this.dto
            ? data.data.map(item =>
                plainToInstance(this.dto, item, {
                  excludeExtraneousValues: true,
                }),
              )
            : data.data;

          return BaseResponse.success(transformedData, data.meta);
        }

        const transformed = this.dto
          ? Array.isArray(data)
            ? data.map(item =>
                plainToInstance(this.dto, item, {
                  excludeExtraneousValues: true,
                }),
              )
            : plainToInstance(this.dto, data, {
                excludeExtraneousValues: true,
              })
          : data;

        return BaseResponse.success(transformed);
      }),
    );
  }
}
