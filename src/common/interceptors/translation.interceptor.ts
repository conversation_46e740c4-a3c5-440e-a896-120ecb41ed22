import { Injectable, NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { I18nContext } from 'nestjs-i18n';
import { TranslationService } from '@/common/services/translation.service';

export interface TranslatableResponse {
  message?: string;
  [key: string]: any;
}

@Injectable()
export class TranslationInterceptor implements NestInterceptor {
  constructor(private readonly translationService: TranslationService) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const i18nContext = I18nContext.current();

    return next.handle().pipe(
      map(data => {
        if (data && typeof data === 'object') {
          return this.translateResponse(data, i18nContext);
        }
        return data;
      }),
    );
  }

  private translateResponse(data: any, i18nContext?: I18nContext): any {
    if (Array.isArray(data)) {
      return data.map(item => this.translateResponse(item, i18nContext));
    }

    if (data && typeof data === 'object') {
      const translated = { ...data };

      // Translate 'message' field if it exists and looks like a translation key
      if (translated.message && typeof translated.message === 'string') {
        translated.message = this.translateMessage(translated.message, i18nContext);
      }

      // Recursively translate nested objects
      Object.keys(translated).forEach(key => {
        if (translated[key] && typeof translated[key] === 'object') {
          translated[key] = this.translateResponse(translated[key], i18nContext);
        }
      });

      return translated;
    }

    return data;
  }

  private translateMessage(message: string, i18nContext?: I18nContext): string {
    // Check if message looks like a translation key
    if (this.isTranslationKey(message)) {
      try {
        return this.translationService.translateKey(message, i18nContext);
      } catch {
        // If translation fails, return original message
        return message;
      }
    }

    return message;
  }

  private isTranslationKey(message: string): boolean {
    // Check if message looks like a translation key (e.g., "auth.LOGIN_SUCCESS", "errors.auth.INVALID_CREDENTIALS")
    return /^[a-zA-Z]+\.[A-Z_]+$/.test(message) || /^[a-zA-Z]+\.[a-zA-Z]+\.[A-Z_]+$/.test(message);
  }
}
