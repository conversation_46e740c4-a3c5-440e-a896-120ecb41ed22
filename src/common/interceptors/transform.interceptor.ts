import { Injectable, NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { BaseResponse } from '../dto/base-response.dto';
import { PaginatedResult } from '../dto/paginated-result.dto';

@Injectable()
export class TransformInterceptor<T> implements NestInterceptor<T, any> {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    return next.handle().pipe(
      map(data => {
        if (data === null || data === undefined) {
          return data;
        }

        if (BaseResponse.isWrapped(data)) {
          return data;
        }

        if (PaginatedResult.isPaginatedResult(data)) {
          const paginatedData = data;
          return BaseResponse.success(paginatedData.data, paginatedData.meta);
        }

        return BaseResponse.success(data);
      }),
    );
  }
}
