import { Get, Post, Body, Patch, Param, Delete, HttpCode, HttpStatus, Query, ParseIntPipe } from '@nestjs/common';
import { BaseEntity } from './base.entity';
import { BaseService } from './base.service';
import { PaginationDto } from '../dto/pagination.dto';
import { DeepPartial } from 'typeorm';
import {
  CreateEntityDoc,
  GetAllEntitiesDoc,
  GetEntityByIdDoc,
  UpdateEntityDoc,
  DeleteEntityDoc,
  SoftDeleteEntityDoc,
  RestoreEntityDoc,
} from '../docs/base.docs';

export interface IBaseController<T extends BaseEntity, CreateDto, UpdateDto> {
  create(createDto: CreateDto): Promise<T>;
  findAll(pagination?: PaginationDto): Promise<T[]>;
  findOne(id: number): Promise<T>;
  update(id: number, updateDto: UpdateDto): Promise<T>;
  remove(id: number): Promise<void>;
  softRemove(id: number): Promise<T>;
  restore(id: number): Promise<T>;
}

export abstract class BaseController<T extends BaseEntity, CreateDto, UpdateDto>
  implements IBaseController<T, CreateDto, UpdateDto>
{
  constructor(
    protected readonly service: BaseService<T>,
    protected readonly entityName: string,
  ) {}

  @CreateEntityDoc()
  @Post()
  async create(@Body() createDto: CreateDto): Promise<T> {
    return this.service.create(createDto as DeepPartial<T>);
  }

  @GetAllEntitiesDoc()
  @Get()
  async findAll(@Query() pagination?: PaginationDto): Promise<T[]> {
    const options = pagination
      ? {
          skip: (pagination.page - 1) * pagination.limit,
          take: pagination.limit,
        }
      : undefined;

    return this.service.findAll(options);
  }

  @GetEntityByIdDoc()
  @Get(':id')
  async findOne(@Param('id', ParseIntPipe) id: number): Promise<T> {
    return this.service.findOne(id);
  }

  @UpdateEntityDoc()
  @Patch(':id')
  async update(@Param('id', ParseIntPipe) id: number, @Body() updateDto: UpdateDto): Promise<T> {
    return this.service.update(id, updateDto as DeepPartial<T>);
  }

  @DeleteEntityDoc()
  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  async remove(@Param('id', ParseIntPipe) id: number): Promise<void> {
    return this.service.remove(id);
  }

  @SoftDeleteEntityDoc()
  @Delete(':id/soft')
  async softRemove(@Param('id', ParseIntPipe) id: number): Promise<T> {
    return this.service.softRemove(id);
  }

  @RestoreEntityDoc()
  @Patch(':id/restore')
  async restore(@Param('id', ParseIntPipe) id: number): Promise<T> {
    return this.service.restore(id);
  }
}
