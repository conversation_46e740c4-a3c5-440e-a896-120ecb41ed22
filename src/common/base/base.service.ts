import { Injectable, NotFoundException } from '@nestjs/common';
import { Repository, FindOptionsWhere, FindManyOptions, DeepPartial } from 'typeorm';
import { BaseEntity } from './base.entity';
import { PaginationDto } from '../dto/pagination.dto';
import { PaginatedResult } from '../dto/paginated-result.dto';

export interface IBaseService<T extends BaseEntity> {
  create(createDto: DeepPartial<T>): Promise<T>;
  findAll(options?: FindManyOptions<T>): Promise<T[]>;
  findOne(id: number): Promise<T>;
  update(id: number, updateDto: DeepPartial<T>): Promise<T>;
  remove(id: number): Promise<void>;
  softRemove(id: number): Promise<T>;
  restore(id: number): Promise<T>;
}

@Injectable()
export abstract class BaseService<T extends BaseEntity> implements IBaseService<T> {
  constructor(protected readonly repository: Repository<T>) {}

  async create(createDto: DeepPartial<T>): Promise<T> {
    const entity = this.repository.create(createDto);
    return this.repository.save(entity);
  }

  async findAll(options?: FindManyOptions<T>): Promise<T[]> {
    const defaultOptions: FindManyOptions<T> = {
      order: { createdAt: 'DESC' } as any,
      withDeleted: false,
    };

    return this.repository.find({
      ...defaultOptions,
      ...options,
    });
  }

  async findAllPaginated(pagination: PaginationDto, options?: FindManyOptions<T>): Promise<PaginatedResult<T>> {
    const defaultOptions: FindManyOptions<T> = {
      withDeleted: false,
    };

    const [items, total] = await this.repository.findAndCount({
      ...defaultOptions,
      ...options,
      skip: pagination.skip,
      take: pagination.limit,
    });

    return new PaginatedResult(items, total, pagination.page, pagination.limit);
  }

  async findOne(id: number): Promise<T> {
    const entity = await this.repository.findOne({
      where: { id } as unknown as FindOptionsWhere<T>,
      withDeleted: false,
    });

    if (!entity) {
      throw new NotFoundException(`${this.getEntityName()} with ID ${id} not found`);
    }

    return entity;
  }

  async update(id: number, updateDto: DeepPartial<T>): Promise<T> {
    const entity = await this.findOne(id);
    Object.assign(entity, updateDto);
    return this.repository.save(entity);
  }

  async remove(id: number): Promise<void> {
    const entity = await this.findOne(id);
    await this.repository.softRemove(entity);
  }

  async softRemove(id: number): Promise<T> {
    const entity = await this.findOne(id);
    return this.repository.softRemove(entity);
  }

  async restore(id: number): Promise<T> {
    const entity = await this.repository.findOne({
      where: { id } as unknown as FindOptionsWhere<T>,
      withDeleted: true,
    });

    if (!entity) {
      throw new NotFoundException(`${this.getEntityName()} with ID ${id} not found`);
    }

    return this.repository.recover(entity);
  }

  async count(options?: FindManyOptions<T>): Promise<number> {
    const defaultOptions: FindManyOptions<T> = {
      withDeleted: false,
    };

    return this.repository.count({
      ...defaultOptions,
      ...options,
    });
  }

  async exists(id: number): Promise<boolean> {
    const count = await this.repository.count({
      where: { id } as unknown as FindOptionsWhere<T>,
      withDeleted: false,
    });
    return count > 0;
  }

  // Helper methods to find with deleted records when needed
  async findAllWithDeleted(options?: FindManyOptions<T>): Promise<T[]> {
    const defaultOptions: FindManyOptions<T> = {
      order: { createdAt: 'DESC' } as any,
      withDeleted: true, // Include soft-deleted records
    };

    return this.repository.find({
      ...defaultOptions,
      ...options,
    });
  }

  async findOneWithDeleted(id: number): Promise<T | null> {
    return this.repository.findOne({
      where: { id } as unknown as FindOptionsWhere<T>,
      withDeleted: true, // Include soft-deleted records
    });
  }

  protected getEntityName(): string {
    return this.repository.metadata.targetName;
  }
}
