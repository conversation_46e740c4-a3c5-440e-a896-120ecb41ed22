import { Exclude } from 'class-transformer';
import { DeleteDateColumn, BaseEntity as TypeOrmBaseEntity, CreateDateColumn, UpdateDateColumn, Index } from 'typeorm';

export abstract class BaseEntity extends TypeOrmBaseEntity {
  @CreateDateColumn({
    type: 'datetime',
    default: () => 'CURRENT_TIMESTAMP',
  })
  @Index('IDX_ENTITY_CREATED_AT')
  createdAt: Date;

  @UpdateDateColumn({
    type: 'datetime',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt: Date;

  @DeleteDateColumn({
    type: 'datetime',
    nullable: true,
  })
  @Exclude()
  deletedAt?: Date;
}
