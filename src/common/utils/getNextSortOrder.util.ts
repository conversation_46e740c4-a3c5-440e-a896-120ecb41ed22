import { Repository } from 'typeorm';
import { ObjectLiteral } from 'typeorm/common/ObjectLiteral';

export async function getNextSortOrder<T extends ObjectLiteral>(
  repository: Repository<T>,
  where?: Record<string, any>,
): Promise<number> {
  const queryBuilder = repository.createQueryBuilder('e').select('MAX(e.sortOrder)', 'max');

  if (where) {
    for (const [key, value] of Object.entries(where)) {
      queryBuilder.andWhere(`e.${key} = :${key}`, { [key]: value });
    }
  }

  const result = await queryBuilder.getRawOne();
  return (parseInt(String(result?.max), 10) || 0) + 1;
}
