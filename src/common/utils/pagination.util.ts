import { IPaginationOptions, IPaginationResult } from '@/common/interfaces/common.interface';

export class PaginationUtil {
  static paginate<T>(data: T[], total: number, options: IPaginationOptions): IPaginationResult<T> {
    const totalPages = Math.ceil(total / options.limit);
    const hasNextPage = options.page < totalPages;
    const hasPreviousPage = options.page > 1;

    return {
      data,
      meta: {
        total,
        page: options.page,
        limit: options.limit,
        totalPages,
        hasNextPage,
        hasPreviousPage,
      },
    };
  }

  static getOffset(page: number, limit: number): number {
    return (page - 1) * limit;
  }

  static validatePaginationOptions(options: IPaginationOptions): void {
    if (options.page < 1) {
      throw new Error('Page must be greater than 0');
    }
    if (options.limit < 1) {
      throw new Error('Limit must be greater than 0');
    }
    if (options.limit > 100) {
      throw new Error('Limit cannot exceed 100');
    }
  }
}
