import { Logger } from '@nestjs/common';

export interface LoggingOptions {
  logArgs?: boolean;
  logResult?: boolean;
  logErrors?: boolean;
  logExecutionTime?: boolean;
  level?: 'log' | 'debug' | 'verbose' | 'warn' | 'error';
  message?: string;
}

export function LogExecution(options: LoggingOptions = {}): MethodDecorator {
  return function (target: Record<string, unknown>, propertyKey: string | symbol, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value as (...args: any[]) => any;

    const className = (target.constructor as { name?: string })?.name ?? 'Unknown';
    const methodName = String(propertyKey);
    const logger = new Logger(`${className}.${methodName}`);

    const {
      logArgs = false,
      logResult = false,
      logErrors = true,
      logExecutionTime = true,
      level = 'log',
      message,
    } = options;

    descriptor.value = async function (this: unknown, ...args: unknown[]): Promise<unknown> {
      const startTime = Date.now();
      const customMessage = message ?? `Executing ${methodName}`;

      try {
        if (logArgs) {
          logger[level](`${customMessage} - Args:`, args);
        } else {
          logger[level](customMessage);
        }

        const result = await originalMethod.apply(this, args);

        if (logExecutionTime) {
          const executionTime = Date.now() - startTime;
          logger[level](`${methodName} completed in ${executionTime}ms`);
        }

        if (logResult) {
          logger[level](`${methodName} result:`, result);
        }

        return result;
      } catch (error: unknown) {
        if (logErrors) {
          const executionTime = Date.now() - startTime;
          const errorMessage = error instanceof Error ? (error.stack ?? error.message) : String(error);
          logger.error(`${methodName} failed after ${executionTime}ms`, errorMessage);
        }
        throw error;
      }
    };

    return descriptor;
  };
}

export function LogAsync(message?: string): MethodDecorator {
  return LogExecution({
    logExecutionTime: true,
    logErrors: true,
    level: 'log',
    message,
  });
}

export function LogDebug(message?: string): MethodDecorator {
  return LogExecution({
    logArgs: true,
    logResult: true,
    logExecutionTime: true,
    logErrors: true,
    level: 'debug',
    message,
  });
}

export function LogPerformance(message?: string): MethodDecorator {
  return LogExecution({
    logExecutionTime: true,
    level: 'verbose',
    message,
  });
}
