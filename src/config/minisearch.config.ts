import { registerAs } from '@nestjs/config';

export interface MiniSearchConfig {
  search: {
    defaultPerPage: number;
    maxPerPage: number;
    fuzzyThreshold: number;
    prefixSearch: boolean;
    enableSuggestions: boolean;
    enableAnalytics: boolean;
    cacheDuration: number;
    maxSuggestions: number;
    combineWith: 'AND' | 'OR';
  };
  indexing: {
    batchSize: number;
    retryAttempts: number;
    retryDelay: number;
    enableAutoReindex: boolean;
    reindexInterval: number; // in hours
    enableIncrementalSync: boolean;
    syncInterval: number; // in minutes
  };
  fields: {
    searchableFields: string[];
    storeFields: string[];
    boost: Record<string, number>;
  };
  performance: {
    maxDocuments: number;
    memoryLimit: number; // in MB
    enableCompression: boolean;
    enableCaching: boolean;
  };
}

export const minisearchConfig = registerAs('minisearch', (): MiniSearchConfig => {
  return {
    search: {
      defaultPerPage: parseInt(process.env.SEARCH_DEFAULT_PER_PAGE ?? '20'),
      maxPerPage: parseInt(process.env.SEARCH_MAX_PER_PAGE ?? '100'),
      fuzzyThreshold: parseFloat(process.env.SEARCH_FUZZY_THRESHOLD ?? '1'),
      prefixSearch: process.env.SEARCH_PREFIX_SEARCH !== 'false',
      enableSuggestions: process.env.SEARCH_ENABLE_SUGGESTIONS !== 'false',
      enableAnalytics: process.env.SEARCH_ENABLE_ANALYTICS === 'true',
      cacheDuration: parseInt(process.env.SEARCH_CACHE_DURATION ?? '300'),
      maxSuggestions: parseInt(process.env.SEARCH_MAX_SUGGESTIONS ?? '10'),
      combineWith: (process.env.SEARCH_COMBINE_WITH as 'AND' | 'OR') ?? 'AND',
    },

    indexing: {
      batchSize: parseInt(process.env.INDEXING_BATCH_SIZE ?? '100'),
      retryAttempts: parseInt(process.env.INDEXING_RETRY_ATTEMPTS ?? '3'),
      retryDelay: parseInt(process.env.INDEXING_RETRY_DELAY ?? '1000'),
      enableAutoReindex: process.env.INDEXING_ENABLE_AUTO_REINDEX === 'true',
      reindexInterval: parseInt(process.env.INDEXING_REINDEX_INTERVAL ?? '24'),
      enableIncrementalSync: process.env.INDEXING_ENABLE_INCREMENTAL_SYNC !== 'false',
      syncInterval: parseInt(process.env.INDEXING_SYNC_INTERVAL ?? '30'),
    },

    fields: {
      searchableFields: ['name', 'description', 'sku', 'barcode', 'categoryName', 'searchKeywords'],
      storeFields: [
        'id',
        'name',
        'description',
        'sku',
        'barcode',
        'price',
        'isActive',
        'categoryId',
        'categoryName',
        'categoryPath',
        'tax',
        'images',
        'createdAt',
        'updatedAt',
      ],
      boost: {
        name: parseFloat(process.env.SEARCH_BOOST_NAME ?? '2.0'),
        sku: parseFloat(process.env.SEARCH_BOOST_SKU ?? '1.5'),
        categoryName: parseFloat(process.env.SEARCH_BOOST_CATEGORY ?? '1.2'),
        description: parseFloat(process.env.SEARCH_BOOST_DESCRIPTION ?? '1.0'),
        searchKeywords: parseFloat(process.env.SEARCH_BOOST_KEYWORDS ?? '1.3'),
        barcode: parseFloat(process.env.SEARCH_BOOST_BARCODE ?? '1.8'),
      },
    },

    performance: {
      maxDocuments: parseInt(process.env.SEARCH_MAX_DOCUMENTS ?? '100000'),
      memoryLimit: parseInt(process.env.SEARCH_MEMORY_LIMIT ?? '512'),
      enableCompression: process.env.SEARCH_ENABLE_COMPRESSION !== 'false',
      enableCaching: process.env.SEARCH_ENABLE_CACHING !== 'false',
    },
  };
});

// Helper function to validate configuration
export function validateMiniSearchConfig(config: MiniSearchConfig): string[] {
  const errors: string[] = [];

  if (config.search.defaultPerPage <= 0) {
    errors.push('search.defaultPerPage must be greater than 0');
  }

  if (config.search.maxPerPage <= 0) {
    errors.push('search.maxPerPage must be greater than 0');
  }

  if (config.search.fuzzyThreshold < 0 || config.search.fuzzyThreshold > 1) {
    errors.push('search.fuzzyThreshold must be between 0 and 1');
  }

  if (config.indexing.batchSize <= 0) {
    errors.push('indexing.batchSize must be greater than 0');
  }

  if (config.indexing.retryAttempts < 0) {
    errors.push('indexing.retryAttempts must be non-negative');
  }

  if (config.fields.searchableFields.length === 0) {
    errors.push('fields.searchableFields cannot be empty');
  }

  if (config.fields.storeFields.length === 0) {
    errors.push('fields.storeFields cannot be empty');
  }

  if (config.performance.maxDocuments <= 0) {
    errors.push('performance.maxDocuments must be greater than 0');
  }

  return errors;
}

export function getDefaultSearchOptions(config: MiniSearchConfig): any {
  return {
    searchableFields: config.fields.searchableFields,
    storeFields: config.fields.storeFields,
    boost: config.fields.boost,
    fuzzy: config.search.fuzzyThreshold,
    prefix: config.search.prefixSearch,
    combineWith: config.search.combineWith,
    fields: config.fields.searchableFields,
  };
}
