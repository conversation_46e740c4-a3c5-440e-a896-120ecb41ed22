import { registerAs } from '@nestjs/config';

export interface SecurityConfig {
  jwt: {
    secret: string;
    expiresIn: string;
    refreshSecret: string;
    refreshExpiresIn: string;
  };
  bcrypt: {
    saltRounds: number;
  };
  cors: {
    origin: string | string[];
    credentials: boolean;
  };
  helmet: {
    contentSecurityPolicy: boolean;
    crossOriginEmbedderPolicy: boolean;
  };
  rateLimit: {
    ttl: number;
    limit: number;
    windowMs: number;
  };
}

export const securityConfig = registerAs(
  'security',
  (): SecurityConfig => ({
    jwt: {
      secret: process.env.JWT_SECRET ?? 'default-secret-key',
      expiresIn: process.env.JWT_EXPIRES_IN ?? '24h',
      refreshSecret: process.env.JWT_REFRESH_SECRET ?? 'default-refresh-secret',
      refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN ?? '7d',
    },
    bcrypt: {
      saltRounds: parseInt(process.env.BCRYPT_SALT_ROUNDS ?? '12', 10),
    },
    cors: {
      origin: process.env.CORS_ORIGIN?.split(',') ?? ['http://localhost:3000', 'http://localhost:3001'],
      credentials: process.env.CORS_CREDENTIALS === 'true',
    },
    helmet: {
      contentSecurityPolicy: process.env.NODE_ENV === 'production',
      crossOriginEmbedderPolicy: process.env.NODE_ENV === 'production',
    },
    rateLimit: {
      ttl: parseInt(process.env.RATE_LIMIT_TTL ?? '60', 10),
      limit: parseInt(process.env.RATE_LIMIT_COUNT ?? '100', 10),
      windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS ?? '60000', 10),
    },
  }),
);
