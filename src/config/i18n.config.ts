import { I18nOptions, AcceptLanguageResolver, I18nJsonLoader } from 'nestjs-i18n';
import * as path from 'path';

export const i18nConfig: I18nOptions = {
  fallbackLanguage: 'en',
  loaderOptions: {
    path: path.join(__dirname, '../i18n/'),
    watch: process.env.NODE_ENV === 'development',
  },
  loader: I18nJsonLoader,
  resolvers: [
    {
      use: AcceptLanguageResolver,
      options: ['accept-language'],
    },
  ],
  ...(process.env.NODE_ENV === 'development' && {
    typesOutputPath: path.join(process.cwd(), 'src/generated/i18n.generated.ts'),
  }),
  throwOnMissingKey: false,
  logging: process.env.NODE_ENV === 'development',
};
