import { registerAs } from '@nestjs/config';
import { join } from 'path';

export interface AppConfig {
  name: string;
  version: string;
  port: number;
  globalPrefix: string;
  env: string;
  timezone: string;
  locale: string;
  folderImportPath: string;
  batchSize: number;
}

export const appConfig = registerAs(
  'app',
  (): AppConfig => ({
    name: process.env.APP_NAME ?? 'HungDong POS Server',
    version: process.env.APP_VERSION ?? '1.0.0',
    port: parseInt(process.env.PORT ?? '3000', 10),
    globalPrefix: 'api',
    env: process.env.NODE_ENV ?? 'development',
    timezone: process.env.APP_TIMEZONE ?? 'UTC',
    locale: process.env.APP_LOCALE ?? 'en',
    folderImportPath: process.env.FOLDER_IMPORT_PATH ?? join(process.cwd(), 'data'),
    batchSize: parseInt(process.env.BATCH_SIZE ?? '1000', 10),
  }),
);
