import { registerAs } from '@nestjs/config';
import { join } from 'path';

export interface IImageProcessingOptions {
  thumbnail: {
    width: number;
    height: number;
    quality: number;
  };
  medium: {
    width: number;
    height: number;
    quality: number;
  };
  large: {
    width: number;
    height: number;
    quality: number;
  };
}

export interface UploadConfig {
  storage: {
    destination: string;
    publicPath: string;
    maxFileSize: number; // in bytes
    maxFiles: number;
  };
  allowedMimeTypes: string[];
  allowedExtensions: string[];
  imageProcessing: {
    enabled: boolean;
    formats: string[];
    quality: number;
    sizes: IImageProcessingOptions;
  };
  security: {
    sanitizeFilename: boolean;
    preventDuplicates: boolean;
    virusScan: boolean;
  };
  cleanup: {
    enabled: boolean;
    maxAge: number; // in days
    orphanedFiles: boolean;
  };
}

export const uploadConfig = registerAs(
  'upload',
  (): UploadConfig => ({
    storage: {
      destination: process.env.UPLOAD_DESTINATION ?? join(process.cwd(), 'uploads'),
      publicPath: process.env.UPLOAD_PUBLIC_PATH ?? '/uploads',
      maxFileSize: parseInt(process.env.UPLOAD_MAX_FILE_SIZE ?? '10485760', 10), // 10MB default
      maxFiles: parseInt(process.env.UPLOAD_MAX_FILES ?? '10', 10),
    },
    allowedMimeTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'],
    allowedExtensions: ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg'],
    imageProcessing: {
      enabled: process.env.IMAGE_PROCESSING_ENABLED !== 'false',
      formats: ['jpeg', 'png', 'webp'],
      quality: parseInt(process.env.IMAGE_QUALITY ?? '85', 10),
      sizes: {
        thumbnail: {
          width: parseInt(process.env.THUMBNAIL_WIDTH ?? '150', 10),
          height: parseInt(process.env.THUMBNAIL_HEIGHT ?? '150', 10),
          quality: parseInt(process.env.THUMBNAIL_QUALITY ?? '80', 10),
        },
        medium: {
          width: parseInt(process.env.MEDIUM_WIDTH ?? '500', 10),
          height: parseInt(process.env.MEDIUM_HEIGHT ?? '500', 10),
          quality: parseInt(process.env.MEDIUM_QUALITY ?? '85', 10),
        },
        large: {
          width: parseInt(process.env.LARGE_WIDTH ?? '1200', 10),
          height: parseInt(process.env.LARGE_HEIGHT ?? '1200', 10),
          quality: parseInt(process.env.LARGE_QUALITY ?? '90', 10),
        },
      },
    },
    security: {
      sanitizeFilename: process.env.SANITIZE_FILENAME !== 'false',
      preventDuplicates: process.env.PREVENT_DUPLICATES !== 'false',
      virusScan: process.env.VIRUS_SCAN_ENABLED === 'true',
    },
    cleanup: {
      enabled: process.env.FILE_CLEANUP_ENABLED !== 'false',
      maxAge: parseInt(process.env.FILE_MAX_AGE_DAYS ?? '30', 10),
      orphanedFiles: process.env.CLEANUP_ORPHANED_FILES !== 'false',
    },
  }),
);

// Constants for file upload validation
export const UPLOAD_CONSTANTS = {
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  MAX_FILES_PER_REQUEST: 10,
  ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
  ALLOWED_EXTENSIONS: ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg'],
  IMAGE_SIZES: {
    THUMBNAIL: { width: 150, height: 150 },
    MEDIUM: { width: 500, height: 500 },
    LARGE: { width: 1200, height: 1200 },
  },
  FILENAME_MAX_LENGTH: 255,
  UPLOAD_TIMEOUT: 30000, // 30 seconds
} as const;

// File type validation patterns
export const FILE_VALIDATION_PATTERNS = {
  IMAGE_EXTENSIONS: /\.(jpg|jpeg|png|gif|webp|svg)$/i,
  SAFE_FILENAME: /^[a-zA-Z0-9._-]+$/,
  DANGEROUS_EXTENSIONS: /\.(exe|bat|cmd|com|pif|scr|vbs|js|jar|php|asp|aspx|jsp)$/i,
} as const;

// Error messages for file upload
export const UPLOAD_ERROR_MESSAGES = {
  FILE_TOO_LARGE: 'File size exceeds maximum allowed size',
  INVALID_FILE_TYPE: 'File type is not allowed',
  TOO_MANY_FILES: 'Too many files uploaded',
  INVALID_FILENAME: 'Invalid filename format',
  UPLOAD_FAILED: 'File upload failed',
  PROCESSING_FAILED: 'Image processing failed',
  STORAGE_ERROR: 'Storage error occurred',
  VIRUS_DETECTED: 'Virus detected in uploaded file',
} as const;
