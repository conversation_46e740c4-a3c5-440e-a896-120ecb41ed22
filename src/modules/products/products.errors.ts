import { BaseError } from '@/core/errors/base.error';
import { defineErrors } from '@/core/errors/define-errors';
import { HttpStatus } from '@nestjs/common';

export const ProductErrors = defineErrors('product', {
  PRODUCT_NOT_FOUND: (id: number): BaseError => {
    return new BaseError('product.PRODUCT_NOT_FOUND', { id }, [], HttpStatus.NOT_FOUND);
  },
  PRODUCT_ALREADY_EXISTS: (name: string): BaseError => {
    return new BaseError('product.PRODUCT_ALREADY_EXISTS', { name }, [], HttpStatus.CONFLICT);
  },
});
