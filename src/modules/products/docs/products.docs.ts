import { applyDecorators, HttpStatus } from '@nestjs/common';
import {
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBody,
  ApiBadRequestResponse,
  ApiNotFoundResponse,
  ApiConflictResponse,
} from '@nestjs/swagger';
import { Product } from '../entities/product.entity';
import { CreateProductDto } from '../dto/create-product.dto';
import { UpdateProductDto } from '../dto/update-product.dto';

export function CreateProductDoc(): MethodDecorator {
  return applyDecorators(
    ApiOperation({ summary: 'Create a new product' }),
    ApiBody({ type: CreateProductDto }),
    ApiResponse({
      status: HttpStatus.CREATED,
      description: 'Product created successfully',
      type: Product,
    }),
    ApiBadRequestResponse({ description: 'Validation error' }),
    ApiConflictResponse({ description: 'Product SKU already exists' }),
  );
}

export function GetAllProductsDoc(): MethodDecorator {
  return applyDecorators(
    ApiOperation({ summary: 'Get all products with pagination and filters' }),
    ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number' }),
    ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page' }),
    ApiQuery({ name: 'search', required: false, type: String, description: 'Search by name or SKU' }),
    ApiQuery({ name: 'categoryId', required: false, type: String, description: 'Filter by category ID' }),
    ApiQuery({ name: 'active', required: false, type: Boolean, description: 'Filter by active status' }),
    ApiQuery({ name: 'inStock', required: false, type: Boolean, description: 'Filter by stock availability' }),
    ApiResponse({
      status: HttpStatus.OK,
      description: 'Products retrieved successfully',
      type: [Product],
    }),
  );
}

export function GetProductByIdDoc(): MethodDecorator {
  return applyDecorators(
    ApiOperation({ summary: 'Get product by ID' }),
    ApiParam({ name: 'id', description: 'Product ID', type: String }),
    ApiResponse({
      status: HttpStatus.OK,
      description: 'Product retrieved successfully',
      type: Product,
    }),
    ApiNotFoundResponse({ description: 'Product not found' }),
  );
}

export function UpdateProductDoc(): MethodDecorator {
  return applyDecorators(
    ApiOperation({
      summary: 'Update product',
      description: 'Update any product fields including name, price, cost, isActive, isFeatured, etc.',
    }),
    ApiParam({ name: 'id', description: 'Product ID', type: String }),
    ApiBody({ type: UpdateProductDto }),
    ApiResponse({
      status: HttpStatus.OK,
      description: 'Product updated successfully',
      type: Product,
    }),
    ApiBadRequestResponse({ description: 'Validation error' }),
    ApiNotFoundResponse({ description: 'Product not found' }),
    ApiConflictResponse({ description: 'Barcode already exists' }),
  );
}

export function DeleteProductDoc(): MethodDecorator {
  return applyDecorators(
    ApiOperation({ summary: 'Delete product' }),
    ApiParam({ name: 'id', description: 'Product ID', type: String }),
    ApiResponse({
      status: HttpStatus.NO_CONTENT,
      description: 'Product deleted successfully',
    }),
    ApiNotFoundResponse({ description: 'Product not found' }),
  );
}

export function GetProductsByCategoryDoc(): MethodDecorator {
  return applyDecorators(
    ApiOperation({ summary: 'Get products by category' }),
    ApiParam({ name: 'categoryId', description: 'Category ID', type: String }),
    ApiQuery({ name: 'active', required: false, type: Boolean, description: 'Filter by active status' }),
    ApiResponse({
      status: HttpStatus.OK,
      description: 'Products retrieved successfully',
      type: [Product],
    }),
    ApiNotFoundResponse({ description: 'Category not found' }),
  );
}

export function GetActiveProductsDoc(): MethodDecorator {
  return applyDecorators(
    ApiOperation({ summary: 'Get all active products' }),
    ApiResponse({
      status: HttpStatus.OK,
      description: 'Active products retrieved successfully',
      type: [Product],
    }),
  );
}

export function GetFeaturedProductsDoc(): MethodDecorator {
  return applyDecorators(
    ApiOperation({ summary: 'Get all featured products' }),
    ApiResponse({
      status: HttpStatus.OK,
      description: 'Featured products retrieved successfully',
      type: [Product],
    }),
  );
}
