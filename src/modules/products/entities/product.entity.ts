import {
  Entity,
  Column,
  Index,
  ManyToOne,
  JoinColumn,
  // OneToMany,
  Check,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { BaseEntity } from '@/common/base/base.entity';
import { Category } from '@/modules/categories/entities/category.entity';
import { Expose } from 'class-transformer';
// import { OrderItem } from '@/modules/orders/entities/order-item.entity';

@Entity('products')
@Index(['name', 'deletedAt'])
@Index(['categoryId'])
@Check('"price" >= 0')
export class Product extends BaseEntity {
  @Expose()
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Expose()
  @Column({ type: 'varchar', length: 200 })
  @Index('IDX_PRODUCT_NAME')
  name: string;

  @Expose()
  @Column({ type: 'text', nullable: true })
  description?: string;

  @Expose()
  @Column({
    type: 'decimal',
    precision: 12,
    scale: 2,
    comment: 'Product price in currency units',
  })
  price: number;

  @Expose()
  @Column({ type: 'boolean', default: true })
  @Index('IDX_PRODUCT_ACTIVE')
  isActive: boolean;

  @Expose()
  @Column({ type: 'integer', default: 0 })
  tax: number;

  @Expose()
  @Column({ type: 'varchar', length: 500, nullable: true })
  imageUrl?: string;

  @Column({ type: 'integer' })
  @Index('IDX_PRODUCT_CATEGORY')
  categoryId: number;

  @Expose()
  @ManyToOne(() => Category, { eager: true })
  @JoinColumn({ name: 'categoryId' })
  category: Category;

  // @OneToMany(() => OrderItem, orderItem => orderItem.product)
  // orderItems: OrderItem[];
}
