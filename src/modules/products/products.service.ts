import { Injectable, NotFoundException, Logger, Inject } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { FindManyOptions, Repository, FindOptionsWhere } from 'typeorm';
import { Product } from './entities/product.entity';
import { CreateProductDto } from './dto/create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';
import { PaginatedResult } from '@/common/dto/paginated-result.dto';
import { PaginationDto } from '@/common/dto/pagination.dto';
import { ProductErrors } from './products.errors';
import { SearchEventHandlers } from '../search/interfaces/search.interface';

@Injectable()
export class ProductsService {
  private readonly logger = new Logger(ProductsService.name);

  constructor(
    @InjectRepository(Product)
    private readonly productRepository: Repository<Product>,
    @Inject('SEARCH_EVENT_HANDLERS') private readonly handlers: SearchEventHandlers,
  ) {}

  async create(createProductDto: CreateProductDto): Promise<Product> {
    if (createProductDto.id) {
      const existingProductByBarcode = await this.productRepository.findOne({
        where: { id: createProductDto.id },
        withDeleted: false,
      });

      if (existingProductByBarcode) {
        throw ProductErrors.PRODUCT_ALREADY_EXISTS(createProductDto.id);
      }
    }

    const product = this.productRepository.create(createProductDto);
    const savedProduct = await this.productRepository.save(product);

    try {
      await this.handlers.onProductCreated(savedProduct);
      this.logger.log(`Product indexed in search: ${savedProduct.id}`);
    } catch (error) {
      this.logger.warn(`Failed to index product in search: ${error.message}`);
    }

    return savedProduct;
  }

  async findAll(options?: FindManyOptions<Product>): Promise<Product[]> {
    const defaultOptions: FindManyOptions<Product> = {
      relations: ['category'],
      order: { createdAt: 'DESC' },
      withDeleted: false,
    };

    return this.productRepository.find({
      ...defaultOptions,
      ...options,
    });
  }

  async findOne(id: string): Promise<Product> {
    const product = await this.productRepository.findOne({
      where: { id },
      relations: ['category'],
      withDeleted: false,
    });
    if (!product) {
      throw ProductErrors.PRODUCT_NOT_FOUND(id);
    }
    return product;
  }

  async update(id: string, updateProductDto: UpdateProductDto): Promise<Product> {
    if (updateProductDto.id) {
      const existingProduct = await this.productRepository.findOne({
        where: { id: updateProductDto.id },
        withDeleted: false,
      });

      if (existingProduct && existingProduct.id !== id) {
        throw ProductErrors.PRODUCT_ALREADY_EXISTS(updateProductDto.id);
      }
    }

    await this.productRepository.update(id, updateProductDto);
    const updatedProduct = await this.findOne(id);

    try {
      await this.handlers.onProductUpdated(updatedProduct);
      this.logger.log(`Product updated and indexed in search: ${updatedProduct.id}`);
    } catch (error) {
      this.logger.warn(`Failed to index updated product in search: ${error.message}`);
    }

    return updatedProduct;
  }

  async remove(id: string): Promise<void> {
    const product = await this.findOne(id);
    await this.productRepository.softRemove(product);
  }

  async count(options?: FindManyOptions<Product>): Promise<number> {
    const defaultOptions: FindManyOptions<Product> = {
      withDeleted: false,
    };

    return this.productRepository.count({
      ...defaultOptions,
      ...options,
    });
  }

  async softRemove(id: string): Promise<Product> {
    const product = await this.findOne(id);
    return this.productRepository.softRemove(product);
  }

  async restore(id: string): Promise<Product> {
    const product = await this.productRepository.findOne({
      where: { id } as FindOptionsWhere<Product>,
      withDeleted: true,
    });

    if (!product) {
      throw new NotFoundException(`Product with ID ${id} not found`);
    }

    return this.productRepository.recover(product);
  }

  async getActiveProductsPaginated(pagination: PaginationDto): Promise<PaginatedResult<Product>> {
    const [categories, total] = await this.productRepository.findAndCount({
      where: { isActive: true },
      order: { createdAt: 'DESC' },
      skip: pagination.skip,
      take: pagination.limit,
      withDeleted: false,
    });

    return new PaginatedResult(categories, total, pagination.page, pagination.limit);
  }

  async findAllPaginated(pagination: PaginationDto): Promise<PaginatedResult<Product>> {
    const [users, total] = await this.productRepository.findAndCount({
      order: { createdAt: 'DESC' },
      skip: pagination.skip,
      take: pagination.limit,
    });

    return new PaginatedResult(users, total, pagination.page, pagination.limit);
  }

  async getActiveProducts(): Promise<Product[]> {
    return this.productRepository.find({
      where: { isActive: true },
      order: { createdAt: 'DESC' },
      withDeleted: false,
    });
  }
}
