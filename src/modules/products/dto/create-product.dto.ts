import { IsString, IsOptional, IsBoolean, IsUrl, Is<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsInt } from 'class-validator';
import { Type } from 'class-transformer';

export class CreateProductDto {
  @IsString()
  id: string;

  @IsString()
  @MinLength(2, { message: 'Product name must be at least 2 characters long' })
  @MaxLength(200, { message: 'Product name must not exceed 200 characters' })
  name: string;

  @IsOptional()
  @IsString()
  @MaxLength(1000, { message: 'Description must not exceed 1000 characters' })
  description?: string;

  @Type(() => Number)
  @IsPositive({ message: 'Price must be a positive number' })
  price: number;

  @IsOptional()
  @Type(() => Number)
  tax?: number;

  @Type(() => Number)
  @IsInt({ message: 'Category ID must be a valid integer' })
  @IsPositive({ message: 'Category ID must be positive' })
  categoryId: number;

  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @IsOptional()
  @IsUrl({}, { message: 'Image URL must be a valid URL' })
  @MaxLength(500, { message: 'Image URL must not exceed 500 characters' })
  imageUrl?: string;
}
