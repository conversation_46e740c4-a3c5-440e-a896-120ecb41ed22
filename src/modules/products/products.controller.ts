import { Controller, Get, Post, Body, Patch, Param, Query, Delete, HttpCode, HttpStatus } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { PaginationDto } from '@/common/dto/pagination.dto';
import { ProductsService } from './products.service';
import { Product } from './entities/product.entity';
import { CreateProductDto } from './dto/create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';
import {
  CreateProductDoc,
  GetAllProductsDoc,
  GetProductByIdDoc,
  UpdateProductDoc,
  DeleteProductDoc,
} from './docs/products.docs';
import { PaginatedResult } from '@/common/dto/paginated-result.dto';
import { TransformResponse } from '@/common/decorators/transform-response.decorator';

@ApiTags('Products')
@Controller({ path: 'api/products', version: '1' })
export class ProductsController {
  constructor(private readonly productsService: ProductsService) {}

  @CreateProductDoc()
  @Post()
  @TransformResponse(Product)
  async create(@Body() createProductDto: CreateProductDto): Promise<Product> {
    return this.productsService.create(createProductDto);
  }

  @GetAllProductsDoc()
  @Get()
  @TransformResponse(Product)
  async findAll(
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('active') activeParam?: string,
  ): Promise<Product[] | PaginatedResult<Product>> {
    const active = activeParam === 'true' ? true : activeParam === 'false' ? false : undefined;

    const pagination = new PaginationDto();
    if (page) pagination.page = page;
    if (limit) pagination.limit = limit;

    if (pagination.page || pagination.limit) {
      if (active !== undefined) {
        return active
          ? this.productsService.getActiveProductsPaginated(pagination)
          : this.productsService.findAllPaginated(pagination);
      }
      return this.productsService.findAllPaginated(pagination);
    }

    if (active !== undefined) {
      return active ? this.productsService.getActiveProducts() : this.productsService.findAll();
    }
    return this.productsService.findAll();
  }

  @GetProductByIdDoc()
  @Get(':id')
  @TransformResponse(Product)
  async findOne(@Param('id') id: string): Promise<Product> {
    return this.productsService.findOne(id);
  }

  @UpdateProductDoc()
  @Patch(':id')
  @TransformResponse(Product)
  async update(@Param('id') id: string, @Body() updateProductDto: UpdateProductDto): Promise<Product> {
    return this.productsService.update(id, updateProductDto);
  }

  @DeleteProductDoc()
  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  async remove(@Param('id') id: string): Promise<void> {
    return this.productsService.remove(id);
  }
}
