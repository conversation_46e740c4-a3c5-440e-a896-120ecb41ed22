import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Between, EntityManager, Repository } from 'typeorm';
import { Order } from './entities/order.entity';
import { OrderItem } from './entities/order-item.entity';
import { UsersService } from '../users';
import { ProductsService } from '../products/products.service';
import { BaseService } from '@/common/base';
import { CreateOrderDto } from './dto/create-order.dto';
import { OrderErrors } from './orders.errors';
import { Product } from '../products/entities/product.entity';

@Injectable()
export class OrdersService extends BaseService<Order> {
  private readonly logger = new Logger(OrdersService.name);

  constructor(
    @InjectRepository(Order)
    private readonly orderRepo: Repository<Order>,
    @InjectRepository(OrderItem)
    private readonly orderItemRepo: Repository<OrderItem>,
    private readonly usersService: UsersService,
    private readonly productsService: ProductsService,
    private readonly entityManager: EntityManager,
  ) {
    super(orderRepo);
  }

  async create(createOrderDto: CreateOrderDto): Promise<Order> {
    return this.entityManager.transaction(async transactionalEntityManager => {
      try {
        this.logger.log(`Creating order for cashier: ${createOrderDto.cashierId}`);

        await this.validateCashier(createOrderDto.cashierId);
        const productDetails = await this.validateAndGetProductDetails(createOrderDto.items);

        const orderNumber = await this.generateOrderNumber();

        const order = this.buildOrder(createOrderDto, orderNumber);
        this.addOrderItems(order, productDetails);
        order.calculateTotals();
        const savedOrder = await transactionalEntityManager.save(Order, order);

        this.logger.log(`Order created successfully with ID: ${savedOrder.id}`);
        return savedOrder;
      } catch (error) {
        this.logger.error(`Failed to create order: ${error.message}`, error.stack);
        throw error;
      }
    });
  }

  private async validateCashier(cashierId: number): Promise<void> {
    await this.usersService.findOne(cashierId);
  }

  private async validateAndGetProductDetails(items: CreateOrderDto['items']): Promise<CreateOrderDto['items']> {
    const productDetails = await Promise.all(
      items.map(async item => {
        const product = await this.productsService.findOne(item.productId);
        this.validateProductForOrder(product, item);
        return { ...item, product };
      }),
    );

    return productDetails;
  }

  private validateProductForOrder(product: Product, item: any): void {
    if (!product.isActive) {
      throw OrderErrors.PRODUCT_NOT_ACTIVE(item.productId);
    }

    if (item.quantity <= 0) {
      throw OrderErrors.INVALID_QUANTITY(item.productId);
    }
  }

  private async generateOrderNumber(): Promise<string> {
    const today = new Date();
    const dateStr = today.toISOString().slice(0, 10).replace(/-/g, '');

    const startOfDay = new Date(today);
    startOfDay.setHours(0, 0, 0, 0);
    const endOfDay = new Date(today);
    endOfDay.setHours(23, 59, 59, 999);

    const todayCount = await this.orderRepo.count({
      where: {
        createdAt: Between(startOfDay, endOfDay),
      },
    });

    const sequence = (todayCount + 1).toString().padStart(3, '0');
    return `ORD-${dateStr}-${sequence}`;
  }

  private buildOrder(createOrderDto: CreateOrderDto, orderNumber: string): Order {
    const order = this.orderRepo.create({
      ...createOrderDto,
      orderNumber,
      items: [],
    });

    return order;
  }

  private addOrderItems(order: Order, productDetails: any[]): void {
    for (const detail of productDetails) {
      const orderItem = this.orderItemRepo.create({
        productId: detail.product.id,
        productName: detail.product.name,
        qty: detail.quantity,
        price: detail.product.price,
        tax: detail.product.tax ?? 0,
        notes: detail.notes,
      });

      orderItem.calculateTotalPrice();
      order.addItem(orderItem);
    }
  }
}
