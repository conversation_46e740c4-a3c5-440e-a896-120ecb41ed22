import { BaseError } from '@/core/errors/base.error';
import { defineErrors } from '@/core/errors/define-errors';
import { HttpStatus } from '@nestjs/common';

export const OrderErrors = defineErrors('order', {
  ORDER_NOT_FOUND: (id: number): BaseError => {
    return new BaseError('order.ORDER_NOT_FOUND', { id }, [], HttpStatus.NOT_FOUND);
  },
  ORDER_ITEM_NOT_FOUND: (id: number): BaseError => {
    return new BaseError('order.ORDER_ITEM_NOT_FOUND', { id }, [], HttpStatus.NOT_FOUND);
  },
  PRODUCT_NOT_FOUND: (id: number): BaseError => {
    return new BaseError('order.PRODUCT_NOT_FOUND', { id }, [], HttpStatus.NOT_FOUND);
  },
  CASHIER_NOT_FOUND: (id: number): BaseError => {
    return new BaseError('order.CASHIER_NOT_FOUND', { id }, [], HttpStatus.NOT_FOUND);
  },
  CUSTOMER_NOT_FOUND: (id: number): BaseError => {
    return new BaseError('order.CUSTOMER_NOT_FOUND', { id }, [], HttpStatus.NOT_FOUND);
  },
  INVALID_ORDER_STATUS: (status: string): BaseError => {
    return new BaseError('order.INVALID_ORDER_STATUS', { status }, [], HttpStatus.BAD_REQUEST);
  },
  INVALID_PAYMENT_METHOD: (method: string): BaseError => {
    return new BaseError('order.INVALID_PAYMENT_METHOD', { method }, [], HttpStatus.BAD_REQUEST);
  },
});
