import {
  IsString,
  IsOptional,
  IsEnum,
  IsArray,
  ValidateNested,
  IsPositive,
  IsInt,
  Min,
  MaxLength,
} from 'class-validator';
import { Type } from 'class-transformer';
import { PaymentMethod } from '../entities/order.entity';

export class CreateOrderItemDto {
  @Type(() => String)
  @IsString()
  productId: string;

  @Type(() => Number)
  @IsInt()
  @IsPositive({ message: 'Quantity must be a positive integer' })
  quantity: number;

  @IsOptional()
  @IsString()
  @MaxLength(500, { message: 'Notes must not exceed 500 characters' })
  notes?: string;
}

export class CreateOrderDto {
  @IsOptional()
  customerId?: number;

  @Type(() => Number)
  @IsInt({ message: 'Cashier ID must be a valid integer' })
  @IsPositive({ message: 'Cashier ID must be positive' })
  cashierId: number;

  @IsOptional()
  @IsEnum(PaymentMethod)
  paymentMethod?: PaymentMethod;

  @IsOptional()
  @Type(() => Number)
  @Min(0, { message: 'Tax amount must be non-negative' })
  taxAmount?: number;

  @IsOptional()
  @Type(() => Number)
  @Min(0, { message: 'Discount amount must be non-negative' })
  discountAmount?: number;

  @IsOptional()
  @IsString()
  @MaxLength(1000, { message: 'Notes must not exceed 1000 characters' })
  notes?: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateOrderItemDto)
  items: CreateOrderItemDto[];
}
