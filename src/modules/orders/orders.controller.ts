import { Body, Controller, Post } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';

import { BaseController } from '@/common/base/base.controller';
import { OrdersService } from './orders.service';
import { Order } from './entities/order.entity';
import { CreateOrderDto } from './dto/create-order.dto';
import { TransformResponse } from '@/common/decorators/transform-response.decorator';
import { CreateNewOrderDoc } from './docs/orders.docs';
import { UpdateOrderDto } from './dto/update-order.dto';

@ApiTags('Orders')
@Controller({ path: 'api/orders', version: '1' })
export class OrdersController extends BaseController<Order, CreateOrderDto, UpdateOrderDto> {
  constructor(private readonly orderService: OrdersService) {
    super(orderService, 'Order');
  }

  @CreateNewOrderDoc()
  @Post()
  @TransformResponse(Order)
  async create(@Body() createOrderDto: CreateOrderDto): Promise<Order> {
    return this.orderService.create(createOrderDto);
  }
}
