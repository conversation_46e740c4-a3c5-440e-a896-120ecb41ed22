import { Entity, Column, Index, ManyToOne, OneToMany, <PERSON>inColumn, PrimaryGeneratedColumn } from 'typeorm';
import { BaseEntity } from '@/common/base/base.entity';
import { User } from '@/modules/users/entities/user.entity';
import { OrderItem } from './order-item.entity';
import { Expose } from 'class-transformer';

export enum OrderStatus {
  PENDING = 'pending',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  REFUNDED = 'refunded',
}

export enum PaymentMethod {
  CASH = 'cash',
  CARD = 'card',
  DIGITAL = 'digital',
}

@Entity('orders')
@Index(['orderNumber', 'deletedAt'])
@Index(['customerId'])
@Index(['cashierId'])
@Index(['status'])
export class Order extends BaseEntity {
  @Expose()
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', length: 20, unique: true })
  orderNumber: string;

  @Column({ type: 'integer', nullable: true })
  customerId?: number;

  @Column({ type: 'integer' })
  cashierId: number;

  @Column({
    type: 'varchar',
    length: 20,
    enum: OrderStatus,
    default: OrderStatus.COMPLETED,
  })
  status: OrderStatus;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  subtotal: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  taxAmount: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  discountAmount: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  grandTotal: number;

  @Column({
    type: 'varchar',
    length: 20,
    enum: PaymentMethod,
    default: PaymentMethod.CASH,
  })
  paymentMethod: PaymentMethod;

  @Column({ type: 'text', nullable: true })
  notes?: string;

  @Column({ type: 'datetime', nullable: true })
  completedAt?: Date;

  // Relations
  @ManyToOne(() => User, { eager: true })
  @JoinColumn({ name: 'cashierId' })
  cashier: User;

  @OneToMany(() => OrderItem, orderItem => orderItem.order, {
    cascade: true,
    eager: true,
  })
  items: OrderItem[];

  get itemCount(): number {
    return this.items?.reduce((sum, item) => sum + item.qty, 0) ?? 0;
  }

  complete(): void {
    this.status = OrderStatus.COMPLETED;
    this.completedAt = new Date();
  }

  cancel(): void {
    this.status = OrderStatus.CANCELLED;
  }

  refund(): void {
    this.status = OrderStatus.REFUNDED;
  }

  calculateTotals(): void {
    if (!this.items || this.items.length === 0) {
      this.subtotal = 0;
      this.taxAmount = 0;
      this.grandTotal = 0;
      return;
    }

    this.taxAmount = this.items?.reduce((sum, item) => sum + item.taxAmount, 0) ?? 0;
    this.subtotal = this.items?.reduce((sum, item) => sum + item.totalExclTax, 0) ?? 0;
    this.grandTotal = this.items?.reduce((sum, item) => sum + item.totalInclTax, 0) ?? 0;
  }

  addItem(item: OrderItem): void {
    if (!this.items) this.items = [];
    this.items.push(item);
    this.calculateTotals();
  }

  removeItem(itemId: number): void {
    if (!this.items) return;
    this.items = this.items.filter(item => item.id !== itemId);
    this.calculateTotals();
  }

  applyDiscount(discountAmount: number): void {
    this.discountAmount = discountAmount;
    this.calculateTotals();
  }
}
