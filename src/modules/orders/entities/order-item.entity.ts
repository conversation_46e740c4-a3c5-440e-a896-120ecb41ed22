import { Entity, Column, Index, ManyToOne, Join<PERSON><PERSON>umn, PrimaryGeneratedColumn } from 'typeorm';
import { BaseEntity } from '@/common/base/base.entity';
import { Order } from './order.entity';
import { Product } from '@/modules/products/entities/product.entity';
import { Expose } from 'class-transformer';

@Entity('order_items')
@Index(['orderId', 'deletedAt'])
@Index(['productId'])
export class OrderItem extends BaseEntity {
  @Expose()
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'integer' })
  orderId: number;

  @Column({ type: 'varchar', length: 36 })
  productId: string;

  @Column({ type: 'varchar', length: 200 })
  productName: string;

  @Column({ type: 'integer' })
  qty: number;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  price: number;

  @Column({ type: 'integer', default: 0 })
  tax: number;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  taxAmount: number;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  totalExclTax: number;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  totalInclTax: number;

  @Column({ type: 'text', nullable: true })
  notes?: string;

  @ManyToOne(() => Order, order => order.items, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'orderId' })
  order: Order;

  @ManyToOne(() => Product, { eager: true })
  @JoinColumn({ name: 'productId' })
  product: Product;

  calculateTotalPrice(): void {
    this.totalInclTax = this.price * this.qty;
    this.taxAmount = this.round2((this.totalInclTax * this.tax) / (100 - this.tax));
    this.totalExclTax = this.totalInclTax - this.taxAmount;
  }

  updateQuantity(newQuantity: number): void {
    this.qty = newQuantity;
    this.calculateTotalPrice();
  }

  updateUnitPrice(newPrice: number): void {
    this.price = newPrice;
    this.calculateTotalPrice();
  }

  private round2(value: number): number {
    return Math.round(value * 100) / 100;
  }
}
