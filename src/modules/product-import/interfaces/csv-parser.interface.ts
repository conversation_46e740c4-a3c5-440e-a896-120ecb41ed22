export interface ICsvRecord {
  upc: string;
  name: string;
  final_price: string;
  percent: string;
}

export interface ICsvParseResult {
  success: boolean;
  data: ICsvRecord[];
  errors: string[];
  totalRows: number;
  validRows: number;
}

export interface ICsvParseError {
  row: number;
  error: string;
  field?: string;
  data?: Record<string, any>;
  timestamp: Date;
}

export interface IFileMetadata {
  path: string;
  name: string;
  size: number;
  hash: string;
  lastModified: Date;
  mimeType?: string;
  originalName?: string;
}

export interface IFileUploadRequest {
  filename: string;
  fullPath: string;
  hash?: string;
}
