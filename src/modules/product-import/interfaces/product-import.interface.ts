import { ICsvParseError } from './csv-parser.interface';

export enum ImportJobStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
}

export interface IImportJobResult {
  id: string;
  version: string;
  status: ImportJobStatus;
  totalRecords: number;
  processedRecords: number;
  insertedRecords: number;
  updatedRecords: number;
  skippedRecords: number;
  errorRecords: number;
  durationMs: number;
  processingRate: number;
  errors?: ICsvParseError[];
}

export interface IImportRecordData {
  id: string;
  name: string;
  price?: number;
  tax?: number;
}
