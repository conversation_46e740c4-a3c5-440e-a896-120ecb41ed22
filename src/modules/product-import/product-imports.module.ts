import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { ProductImportRecord } from './entities/product-import-record.entity';
import { AutoImportService } from './services/auto-import.service';
import { FileHashService } from './services/file-hash.service';
import { ProductImportJob } from './entities/product-import-job.entity';
import { CsvParserService } from './services/csv-parser.service';
import { ProductImportRecordsService } from './services/product-import-records.service';

@Module({
  imports: [TypeOrmModule.forFeature([ProductImportRecord, ProductImportJob])],
  providers: [AutoImportService, FileHashService, CsvParserService, ProductImportRecordsService],
  exports: [AutoImportService],
})
export class ProductImportsModule {}
