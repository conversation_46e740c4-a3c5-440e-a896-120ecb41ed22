import { Entity, Column, Index, PrimaryGeneratedColumn } from 'typeorm';

import { BaseEntity } from '@/common/base';
import { ImportJobStatus } from '../interfaces/product-import.interface';

@Entity('product_import_jobs')
@Index(['status', 'createdAt'])
@Index(['fileHash'])
export class ProductImportJob extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: string;

  @Column({ type: 'varchar', length: 100, unique: true })
  version: string;

  @Column({ type: 'varchar', length: 500 })
  fileName: string;

  @Column({ type: 'varchar', length: 64 })
  fileHash: string;

  @Column({ type: 'bigint' })
  fileSize: number;

  @Column({
    type: 'varchar',
    length: 20,
    enum: ImportJobStatus,
    default: ImportJobStatus.PENDING,
  })
  status: ImportJobStatus;

  @Column({ type: 'integer', default: 0 })
  totalRecords: number;

  @Column({ type: 'integer', default: 0 })
  processedRecords: number;

  @Column({ type: 'integer', default: 0 })
  insertedRecords: number;

  @Column({ type: 'integer', default: 0 })
  updatedRecords: number;

  @Column({ type: 'integer', default: 0 })
  skippedRecords: number;

  @Column({ type: 'integer', default: 0 })
  errorRecords: number;

  @Column({ type: 'integer', default: 1000 })
  batchSize: number;

  @Column({ type: 'datetime', nullable: true })
  startedAt?: Date;

  @Column({ type: 'datetime', nullable: true })
  completedAt?: Date;

  @Column({ type: 'integer', nullable: true })
  durationMs?: number;

  @Column({ type: 'text', nullable: true })
  errorMessage?: string;

  @Column({ type: 'json', nullable: true })
  errorDetails?: Record<string, any>;

  @Column({ type: 'text', nullable: true })
  notes?: string;
}
