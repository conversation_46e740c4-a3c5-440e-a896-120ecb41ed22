import { Injectable, Logger } from '@nestjs/common';
import { createReadStream } from 'fs';
import * as csv from 'csv-parser';
import { ICsvParseError, ICsvRecord } from '../interfaces/csv-parser.interface';
import { IImportRecordData } from '../interfaces/product-import.interface';

@Injectable()
export class CsvParserService {
  private readonly logger = new Logger(CsvParserService.name);

  async *parseCsvInBatches(
    filePath: string,
    batchSize = 1000,
    skipErrors = true,
  ): AsyncGenerator<{ records: IImportRecordData[]; errors: ICsvParseError[]; batchNumber: number }> {
    let currentBatch: IImportRecordData[] = [];
    let errors: ICsvParseError[] = [];
    let rowNumber = 0;
    let batchNumber = 1;

    const stream = createReadStream(filePath).pipe(
      csv({
        headers: ['upc', 'name', 'final_price', 'percent'],
      }),
    );

    for await (const data of stream) {
      rowNumber++;

      if (rowNumber === 1) {
        continue;
      }

      try {
        const productData = this.transformCsvRecord(data as ICsvRecord, rowNumber);

        if (productData) {
          currentBatch.push(productData);
        }
      } catch (error: any) {
        const migrationError: ICsvParseError = {
          row: rowNumber,
          data,
          error: error.message,
          timestamp: new Date(),
        };
        errors.push(migrationError);

        if (!skipErrors) {
          throw error;
        }
      }

      // Yield batch when it reaches the specified size
      if (currentBatch.length >= batchSize) {
        yield {
          records: [...currentBatch],
          errors: [...errors],
          batchNumber: batchNumber++,
        };
        currentBatch = [];
        errors = [];
      }
    }

    if (currentBatch.length > 0 || errors.length > 0) {
      yield {
        records: [...currentBatch],
        errors: [...errors],
        batchNumber: batchNumber++,
      };
    }
  }

  async countCsvRecords(filePath: string): Promise<number> {
    return new Promise((resolve, reject) => {
      let count = 0;

      createReadStream(filePath)
        .pipe(
          csv({
            headers: ['upc', 'name', 'final_price', 'percent'],
          }),
        )
        .on('data', () => {
          count++;
        })
        .on('end', () => {
          // Subtract 1 to account for header row
          resolve(Math.max(0, count - 1));
        })
        .on('error', error => {
          this.logger.error('Error counting CSV records:', error);
          reject(new Error(error?.message ?? 'Unknown CSV parsing error'));
        });
    });
  }

  private transformCsvRecord(record: ICsvRecord, rowNumber: number): IImportRecordData | null {
    try {
      if (!record.upc || !record.name || !record.final_price) {
        throw new Error(`Missing required fields at row ${rowNumber}`);
      }

      const price = parseFloat(record.final_price);
      if (isNaN(price) || price < 0) {
        throw new Error(`Invalid price "${record.final_price}" at row ${rowNumber}`);
      }

      const tax = parseInt(record.percent, 10);
      if (isNaN(tax) || tax < 0 || tax > 100) {
        throw new Error(`Invalid tax percentage "${record.percent}" at row ${rowNumber}`);
      }

      const name = this.cleanProductName(record.name);
      if (!name || name.length < 2) {
        throw new Error(`Invalid product name "${record.name}" at row ${rowNumber}`);
      }

      return {
        id: record.upc,
        name,
        price,
        tax,
      };
    } catch (error) {
      this.logger.warn(`Error transforming record at row ${rowNumber}:`, error.message);
      throw error;
    }
  }

  private cleanProductName(name: string): string {
    return name
      .trim()
      .replace(/\s+/g, ' ') // Replace multiple spaces with single space
      .replace(/[^\w\s\-.,()]/g, '') // Remove special characters except basic punctuation
      .substring(0, 200); // Limit to database field length
  }

  async validateCsvStructure(filePath: string): Promise<{ isValid: boolean; errors: string[] }> {
    const errors: string[] = [];

    try {
      const firstRows: any[] = [];
      let rowCount = 0;

      await new Promise<void>((resolve, reject) => {
        createReadStream(filePath)
          .pipe(
            csv({
              headers: ['upc', 'name', 'final_price', 'percent'],
            }),
          )
          .on('data', data => {
            rowCount++;
            if (rowCount === 1) {
              return;
            }
            if (firstRows.length < 5) {
              firstRows.push(data);
            }
            if (firstRows.length >= 5) {
              resolve();
            }
          })
          .on('end', resolve)
          .on('error', reject);
      });

      if (firstRows.length === 0) {
        errors.push('CSV file is empty or has no valid records');
      } else {
        const firstRow = firstRows[0];
        const requiredColumns = ['upc', 'name', 'final_price', 'percent'];

        for (const column of requiredColumns) {
          if (!(column in firstRow)) {
            errors.push(`Missing required column: ${column}`);
          }
        }
      }

      return {
        isValid: errors.length === 0,
        errors,
      };
    } catch (error: any) {
      errors.push(`Error validating CSV structure: ${error.message}`);
      return {
        isValid: false,
        errors,
      };
    }
  }
}
