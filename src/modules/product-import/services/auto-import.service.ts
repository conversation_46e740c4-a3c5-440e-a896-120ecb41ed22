import { Injectable, Logger, OnApplicationBootstrap } from '@nestjs/common';
import { readdirSync } from 'fs';
import { Repository } from 'typeorm';
import { join } from 'path';
import { InjectRepository } from '@nestjs/typeorm';

import { FileHashService } from './file-hash.service';
import { ProductImportJob } from '../entities/product-import-job.entity';
import { CsvParserService } from './csv-parser.service';
import { ProductImportRecordsService } from './product-import-records.service';
import { ConfigService } from '@nestjs/config';
import { ICsvParseError, IFileMetadata, IFileUploadRequest } from '../interfaces/csv-parser.interface';
import { IImportJobResult, ImportJobStatus } from '../interfaces/product-import.interface';

@Injectable()
export class AutoImportService implements OnApplicationBootstrap {
  private readonly logger = new Logger(AutoImportService.name);
  private readonly folderPath: string;
  private readonly batchSize: number;
  private bootstrapPromise: Promise<void> | null = null;

  constructor(
    @InjectRepository(ProductImportJob)
    private readonly productImportJobRepo: Repository<ProductImportJob>,
    private readonly fileHashService: FileHashService,
    private readonly csvParserService: CsvParserService,
    private readonly productImportRecordsService: ProductImportRecordsService,
    private readonly configService: ConfigService,
  ) {
    this.folderPath = this.configService.get<string>('app.folderImportPath') ?? join(process.cwd(), 'data');
    this.batchSize = this.configService.get<number>('app.batchSize') ?? 1000;
  }

  async onApplicationBootstrap(): Promise<void> {
    this.logger.debug('Application bootstrap started - checking for auto import...');
    this.bootstrapPromise = this.runBootstrapProcess();

    try {
      await this.bootstrapPromise;
    } catch (error) {
      this.logger.error('Auto import failed during bootstrap', error.stack);
    }
  }

  async waitForBootstrapCompletion(): Promise<void> {
    if (this.bootstrapPromise) {
      await this.bootstrapPromise;
    }
  }

  private async runBootstrapProcess(): Promise<void> {
    try {
      await this.runAutoMigration();
    } catch (error) {
      this.logger.error('Auto import failed during bootstrap', error.stack);
      throw error;
    }
  }

  private async runAutoMigration(): Promise<void> {
    try {
      const csvFiles = this.getCsvFiles(this.folderPath);

      if (csvFiles.length === 0) {
        this.logger.log('No CSV files found, skipping migration');
        return;
      }

      for (const file of csvFiles) {
        const fileCheck = await this.checkFileNeeded(file);

        if (!fileCheck.needed) {
          this.logger.log(`No migration needed: ${fileCheck.reason}`);
          return;
        }

        this.logger.log(`File: ${fileCheck.fileInfo?.path}`);
        const result = await this.executeImportData(file.fullPath);
        console.log('🎉 Migration completed successfully!');
        console.log(`📊 Records processed: ${result.processedRecords.toLocaleString()}`);
        console.log(`➕ Records inserted: ${result.insertedRecords.toLocaleString()}`);
        console.log(`🔄 Records updated: ${result.updatedRecords.toLocaleString()}`);
        console.log(`⏭️  Records skipped: ${result.skippedRecords.toLocaleString()}`);

        if (result.errorRecords > 0) {
          console.log(`⚠️  Records with errors: ${result.errorRecords.toLocaleString()}`);
        }

        console.log(`⏱️  Duration: ${(result.durationMs / 1000).toFixed(2)}s`);
        console.log(`🚀 Processing rate: ${result.processingRate.toLocaleString()} records/sec`);
      }
    } catch (error) {
      this.logger.warn(`⚠️  Auto-migration failed: ${error.message}`);
      this.logger.warn('Server will continue without migration data');
      throw error;
    }
  }

  private getCsvFiles(folderPath: string): IFileUploadRequest[] {
    const files = readdirSync(folderPath)
      .filter(file => file.endsWith('.csv'))
      .map(file => ({
        filename: file,
        fullPath: join(folderPath, file),
      }));

    return files;
  }

  private async checkFileNeeded(
    file: IFileUploadRequest,
  ): Promise<{ needed: boolean; reason: string; fileInfo?: IFileMetadata }> {
    try {
      const isValid = await this.fileHashService.validateFile(file.fullPath);
      if (!isValid) {
        return { needed: false, reason: 'File does not exist or is not readable' };
      }

      const fileInfo = await this.fileHashService.getFileInfo(file.fullPath);

      const existingMigration = await this.productImportJobRepo.findOne({
        where: { fileHash: fileInfo.hash, status: ImportJobStatus.COMPLETED },
        order: { createdAt: 'DESC' },
      });

      if (existingMigration) {
        return {
          needed: false,
          reason: `File already migrated (version: ${existingMigration.version})`,
          fileInfo,
        };
      }

      return {
        needed: true,
        reason: 'New file or file has been modified',
        fileInfo,
      };
    } catch (error) {
      this.logger.error('Error checking migration status:', error);
      return { needed: false, reason: `Error: ${error.message}` };
    }
  }

  async executeImportData(filePath: string): Promise<IImportJobResult> {
    const startTime = Date.now();
    const validation = await this.csvParserService.validateCsvStructure(filePath);

    if (!validation.isValid) {
      throw new Error(`Invalid CSV structure: ${validation.errors.join(', ')}`);
    }

    const fileInfo = await this.fileHashService.getFileInfo(filePath);
    const totalRecords = await this.csvParserService.countCsvRecords(filePath);
    const importHistory = await this.createImportHistory(fileInfo, totalRecords);

    try {
      await this.updateImportJobStatus(importHistory.id, ImportJobStatus.RUNNING, { startedAt: new Date() });
      console.log(`📋 Found ${totalRecords.toLocaleString()} valid records to process`);

      const result = await this.processImportBatches(importHistory.id, filePath, totalRecords);
      const endTime = Date.now();
      const durationMs = endTime - startTime;

      await this.updateImportJobStatus(importHistory.id, ImportJobStatus.COMPLETED, {
        completedAt: new Date(),
        durationMs,
        processedRecords: result.processedRecords,
        insertedRecords: result.insertedRecords,
        updatedRecords: result.updatedRecords,
        skippedRecords: result.skippedRecords,
        errorRecords: result.errorRecords,
      });

      const importResult: IImportJobResult = {
        id: importHistory.id,
        version: importHistory.version,
        status: ImportJobStatus.COMPLETED,
        totalRecords,
        processedRecords: result.processedRecords,
        insertedRecords: result.insertedRecords,
        updatedRecords: result.updatedRecords,
        skippedRecords: result.skippedRecords,
        errorRecords: result.errorRecords,
        durationMs,
        processingRate: Math.round((result.processedRecords / durationMs) * 1000),
        errors: result.errors,
      };

      return importResult;
    } catch (error) {
      await this.updateImportJobStatus(importHistory.id, ImportJobStatus.FAILED, {
        errorMessage: error.message,
        errorDetails: { stack: error.stack, timestamp: new Date() },
      });
      throw error;
    }
  }

  private async createImportHistory(fileInfo: IFileMetadata, totalRecords: number): Promise<ProductImportJob> {
    const version = this.fileHashService.generateImportVersion(fileInfo.hash);
    const data = this.productImportJobRepo.create({
      version,
      fileName: fileInfo.name,
      fileHash: fileInfo.hash,
      fileSize: fileInfo.size,
      status: ImportJobStatus.PENDING,
      totalRecords,
      batchSize: this.batchSize,
    });

    return this.productImportJobRepo.save(data);
  }

  private async updateImportJobStatus(
    id: string,
    status: ImportJobStatus,
    updates: Partial<ProductImportJob>,
  ): Promise<void> {
    await this.productImportJobRepo.update(id, {
      status,
      ...updates,
      updatedAt: new Date(),
    });
  }

  private async processImportBatches(
    migrationId: string,
    filePath: string,
    totalRecords: number,
  ): Promise<{
    processedRecords: number;
    insertedRecords: number;
    updatedRecords: number;
    skippedRecords: number;
    errorRecords: number;
    errors: ICsvParseError[];
  }> {
    let totalProcessed = 0;
    let totalInserted = 0;
    let totalUpdated = 0;
    let totalSkipped = 0;
    let totalErrors = 0;
    let lastProgressUpdate = 0;
    const allErrors: ICsvParseError[] = [];

    const batchGenerator = this.csvParserService.parseCsvInBatches(filePath, this.batchSize);

    for await (const { records, errors, batchNumber } of batchGenerator) {
      try {
        const batchResult = await this.productImportRecordsService.syncProducts(records);

        totalProcessed += records.length;
        totalInserted += batchResult.inserted;
        totalUpdated += batchResult.updated;
        totalSkipped += batchResult.skipped;
        allErrors.push(...batchResult.errors, ...errors);

        const progressPercent = Math.floor((totalProcessed / totalRecords) * 100);
        const shouldShowProgress =
          progressPercent >= lastProgressUpdate + 5 || totalProcessed % 5000 === 0 || totalProcessed === totalRecords;

        if (shouldShowProgress) {
          const progressBar = this.generateProgressBar(totalProcessed, totalRecords);

          process.stdout.write(`\r📦 Processing: ${progressBar}`);
          lastProgressUpdate = progressPercent;
        }
      } catch (error) {
        this.logger.error(`Error processing batch ${batchNumber}:`, error);

        allErrors.push({
          row: batchNumber * this.batchSize,
          data: { batchNumber, recordCount: records.length },
          error: `Batch processing error: ${error.message}`,
          timestamp: new Date(),
        });

        totalErrors += records.length;
      }
    }

    if (totalProcessed === totalRecords) {
      const finalProgressBar = this.generateProgressBar(totalProcessed, totalRecords);
      process.stdout.write(`\r📦 Processing: ${finalProgressBar}`);
    }

    process.stdout.write('\n');

    return {
      processedRecords: totalProcessed,
      insertedRecords: totalInserted,
      updatedRecords: totalUpdated,
      skippedRecords: totalSkipped,
      errorRecords: totalErrors,
      errors: allErrors,
    };
  }

  private generateProgressBar(processed: number, total: number): string {
    const percentage = Math.floor((processed / total) * 100);
    const barLength = 20;
    const filledLength = Math.floor((percentage / 100) * barLength);
    const bar = '█'.repeat(filledLength) + '░'.repeat(barLength - filledLength);
    return `${percentage}% |${bar}| ${processed.toLocaleString()}/${total.toLocaleString()}`;
  }
}
