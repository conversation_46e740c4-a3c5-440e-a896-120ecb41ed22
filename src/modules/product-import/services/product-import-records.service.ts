import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';

import { ProductImportRecord } from '../entities/product-import-record.entity';
import { IImportRecordData } from '../interfaces/product-import.interface';
import { ICsvParseError } from '../interfaces/csv-parser.interface';

@Injectable()
export class ProductImportRecordsService {
  private readonly logger = new Logger(ProductImportRecordsService.name);

  constructor(
    @InjectRepository(ProductImportRecord)
    private readonly productImportRecordRepo: Repository<ProductImportRecord>,
  ) {}

  async syncProducts(
    products: IImportRecordData[],
  ): Promise<{ inserted: number; updated: number; skipped: number; errors: ICsvParseError[] }> {
    let inserted = 0;
    let updated = 0;
    let skipped = 0;
    const errors: ICsvParseError[] = [];

    const existingProductImports = await this.productImportRecordRepo.find({
      where: {
        id: In(products.map(p => p.id)),
      },
    });

    const existingByIdMap = new Map(existingProductImports.map(p => [p.id, p]));

    for (const product of products) {
      try {
        const existingProduct = existingByIdMap.get(product.id) ?? null;

        if (existingProduct) {
          Object.assign(existingProduct, product);
          await this.productImportRecordRepo.update(existingProduct.id, existingProduct);
          updated++;
        } else {
          const productImport = this.productImportRecordRepo.create(product);
          await this.productImportRecordRepo.save(productImport);
          inserted++;
        }
      } catch (error) {
        errors.push({
          row: inserted + updated + skipped + errors.length + 1,
          data: product,
          error: error.message,
          timestamp: new Date(),
        });
      }
    }
    return { inserted, updated, skipped, errors };
  }
}
