import { Injectable, Logger } from '@nestjs/common';
import { createHash } from 'crypto';
import { createReadStream, promises as fs } from 'fs';

import { IFileMetadata } from '../interfaces/csv-parser.interface';

@Injectable()
export class FileHashService {
  private readonly logger = new Logger(FileHashService.name);

  async calculateFileHash(filePath: string): Promise<string> {
    return new Promise((resolve, reject) => {
      const hash = createHash('sha256');
      const stream = createReadStream(filePath);

      stream.on('data', data => {
        hash.update(data);
      });

      stream.on('end', () => {
        resolve(hash.digest('hex'));
      });

      stream.on('error', error => {
        this.logger.error(`Error calculating hash for ${filePath}:`, error);
        reject(error);
      });
    });
  }

  async getFileInfo(filePath: string): Promise<IFileMetadata> {
    try {
      const stats = await fs.stat(filePath);
      const hash = await this.calculateFileHash(filePath);
      const name = filePath.split('/').pop() ?? filePath;

      return {
        path: filePath,
        name,
        size: stats.size,
        hash,
        lastModified: stats.mtime,
      };
    } catch (error) {
      this.logger.error(`Error getting file info for ${filePath}:`, error);
      throw error;
    }
  }

  hasFileChanged(currentHash: string, previousHash: string): boolean {
    return currentHash !== previousHash;
  }

  async validateFile(filePath: string): Promise<boolean> {
    try {
      await fs.access(filePath, fs.constants.R_OK);
      const stats = await fs.stat(filePath);
      return stats.isFile() && stats.size > 0;
    } catch (error) {
      this.logger.warn(`File validation failed for ${filePath}:`, error.message);
      return false;
    }
  }

  async getFileSize(filePath: string): Promise<number> {
    try {
      const stats = await fs.stat(filePath);
      return stats.size;
    } catch (error) {
      this.logger.error(`Error getting file size for ${filePath}:`, error);
      throw error;
    }
  }

  formatFileSize(bytes: number): string {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(2)} ${units[unitIndex]}`;
  }

  generateImportVersion(fileHash: string): string {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const shortHash = fileHash.substring(0, 8);
    return `import-${timestamp}-${shortHash}`;
  }
}
