import { applyDecorators, HttpStatus } from '@nestjs/common';
import {
  ApiOperation,
  ApiBody,
  ApiResponse,
  ApiBadRequestResponse,
  ApiUnauthorizedResponse,
  ApiConflictResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { AuthResponseDto, RefreshTokenDto } from '../dto/auth-response.dto';

export function LoginDoc(): MethodDecorator {
  return applyDecorators(
    ApiOperation({ summary: 'User login' }),
    ApiBody({
      description: 'Login credentials',
      schema: {
        type: 'object',
        properties: {
          username: {
            type: 'string',
            example: 'user1',
          },
          password: {
            type: 'string',
            example: '123123',
          },
        },
        required: ['username', 'password'],
      },
    }),
    ApiResponse({
      status: HttpStatus.OK,
      type: AuthResponseDto,
    }),
    ApiConflictResponse({ description: 'User already exists' }),
    ApiBadRequestResponse({ description: 'Validation error' }),
  );
}

export function RegisterDoc(): MethodDecorator {
  return applyDecorators(
    ApiOperation({ summary: 'User registration' }),
    ApiBody({
      schema: {
        type: 'object',
        properties: {
          username: {
            type: 'string',
            example: 'hungdong',
          },
          password: {
            type: 'string',
            example: '123123',
          },
        },
        required: ['username', 'password'],
      },
    }),
    ApiResponse({
      status: HttpStatus.CREATED,
      type: AuthResponseDto,
      schema: {
        example: {},
      },
    }),
    ApiBadRequestResponse({ description: 'Validation error' }),
    ApiConflictResponse({ description: 'User already exists' }),
  );
}

export function RefreshTokenDoc(): MethodDecorator {
  return applyDecorators(
    ApiOperation({ summary: 'Refresh token', description: 'Refresh access token using refresh token' }),
    ApiBody({ schema: { properties: { refreshToken: { type: 'string' } } } }),
    ApiResponse({
      status: HttpStatus.OK,
      description: 'Token refreshed successfully',
      type: RefreshTokenDto,
    }),
    ApiUnauthorizedResponse({ description: 'Invalid refresh token' }),
  );
}

export function LogoutDoc(): MethodDecorator {
  return applyDecorators(
    ApiOperation({ summary: 'User logout', description: 'Logout user and invalidate refresh token' }),
    ApiBearerAuth('JWT-auth'),
    ApiResponse({ status: HttpStatus.OK, description: 'Logout successful' }),
    ApiUnauthorizedResponse({ description: 'Unauthorized' }),
  );
}
