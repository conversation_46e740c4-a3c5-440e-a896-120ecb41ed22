import { HttpStatus } from '@nestjs/common';
import { BaseError } from '@/core/errors/base.error';
import { defineErrors } from '@/core/errors/define-errors';

export const AuthErrors = defineErrors('auth', {
  INVALID_CREDENTIALS: (): BaseError => {
    return new BaseError('auth.INVALID_CREDENTIALS', {}, [], HttpStatus.UNAUTHORIZED);
  },

  EMAIL_ALREADY_REGISTERED: (email: string): BaseError => {
    return new BaseError('auth.EMAIL_ALREADY_REGISTERED', { email }, [], HttpStatus.BAD_REQUEST);
  },

  USER_NOT_FOUND: (username: string): BaseError => {
    return new BaseError('auth.USER_NOT_FOUND', { username }, [], HttpStatus.NOT_FOUND);
  },

  USERNAME_ALREADY_EXISTS: (username: string): BaseError => {
    return new BaseError('auth.USERNAME_ALREADY_EXISTS', { username }, [], HttpStatus.CONFLICT);
  },

  REGISTRATION_FAILED: (reason?: string): BaseError => {
    return new BaseError('auth.REGISTRATION_FAILED', { reason }, [], HttpStatus.BAD_REQUEST);
  },

  USER_CREATION_FAILED: (): BaseError => {
    return new BaseError('auth.USER_CREATION_FAILED', {}, [], HttpStatus.INTERNAL_SERVER_ERROR);
  },

  ACCOUNT_DEACTIVATED: (): BaseError => {
    return new BaseError('auth.ACCOUNT_DEACTIVATED', {}, [], HttpStatus.FORBIDDEN);
  },
});
