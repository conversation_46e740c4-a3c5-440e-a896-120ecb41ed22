import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { ConfigService } from '@nestjs/config';
import { UsersService } from '@/modules/users/users.service';
import { JwtPayload } from '../interfaces/jwt-payload.interface';
import { User } from '@/modules/users/entities/user.entity';
import { AUTH_CONSTANTS } from '@/common/constants/auth.constants';
import { AuthErrors } from '../auth.errors';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy, AUTH_CONSTANTS.JWT_STRATEGY) {
  constructor(
    configService: ConfigService,
    private readonly usersService: UsersService,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: configService.get<string>('JWT_SECRET') ?? 'default-secret',
    });
  }

  async validate(payload: JwtPayload): Promise<User | null> {
    const user = await this.usersService.findByUsername(payload.username);

    if (!user) {
      throw AuthErrors.USER_NOT_FOUND(payload.username);
    }

    if (!user.isActive) {
      throw AuthErrors.ACCOUNT_DEACTIVATED();
    }

    return user;
  }
}
