import { Strategy } from 'passport-local';
import { PassportStrategy } from '@nestjs/passport';
import { Injectable } from '@nestjs/common';
import { User } from '../../users/entities/user.entity';
import { AUTH_CONSTANTS } from '@/common/constants/auth.constants';
import { AuthService } from '../auth.service';
import { AuthErrors } from '../auth.errors';

@Injectable()
export class LocalStrategy extends PassportStrategy(Strategy, AUTH_CONSTANTS.LOCAL_STRATEGY) {
  constructor(private authService: AuthService) {
    super();
  }

  async validate(username: string, password: string): Promise<User> {
    const user = await this.authService.validateUser(username, password);

    if (!user) {
      throw AuthErrors.INVALID_CREDENTIALS();
    }
    return user;
  }
}
