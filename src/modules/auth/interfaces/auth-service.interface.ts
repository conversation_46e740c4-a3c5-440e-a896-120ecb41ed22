import { User } from '../../users/entities/user.entity';
import { RegisterDto } from '../dto/register.dto';
import { AuthResponseDto, RefreshTokenDto } from '../dto/auth-response.dto';

export interface IAuthService {
  validateUser(username: string, password: string): Promise<User | null>;
  login(user: User): Promise<AuthResponseDto>;
  register(registerDto: RegisterDto): Promise<AuthResponseDto>;
  refreshToken(refreshToken: string): Promise<RefreshTokenDto>;
  logout(userId: number): Promise<void>;
}
