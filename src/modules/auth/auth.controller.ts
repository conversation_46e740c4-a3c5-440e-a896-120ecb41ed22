import {
  Controller,
  Post,
  HttpCode,
  HttpStatus,
  Request,
  Body,
  UseGuards,
  UseInterceptors,
  ClassSerializerInterceptor,
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';

import { AuthService } from './auth.service';
import { AuthResponseDto, RefreshTokenDto } from './dto/auth-response.dto';
import { Public } from '../../common/decorators/public.decorator';
import { LoginDoc, LogoutDoc, RefreshTokenDoc, RegisterDoc } from './docs/auth.docs';
import { RegisterDto } from './dto/register.dto';
import { JwtAuthGuard } from '@/common/guards/jwt-auth.guard';
import { LocalAuthGuard } from '@/common/guards/local-auth.guard';
import { User } from '@/modules/users/entities/user.entity';
import { TransformResponse } from '@/common/decorators/transform-response.decorator';

@ApiTags('Authentication')
@Controller({ path: 'api/auth', version: '1' })
@UseInterceptors(ClassSerializerInterceptor)
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @LoginDoc()
  @Public()
  @UseGuards(LocalAuthGuard)
  @Post('login')
  @HttpCode(HttpStatus.OK)
  @TransformResponse(AuthResponseDto)
  async login(@Request() req: { user: User }): Promise<AuthResponseDto> {
    return this.authService.login(req.user);
  }

  @RegisterDoc()
  @Public()
  @Post('register')
  @HttpCode(HttpStatus.CREATED)
  @TransformResponse(AuthResponseDto)
  async register(@Body() registerDto: RegisterDto): Promise<AuthResponseDto> {
    return this.authService.register(registerDto);
  }

  @RefreshTokenDoc()
  @Public()
  @Post('refresh')
  @HttpCode(HttpStatus.OK)
  @TransformResponse(RefreshTokenDto)
  async refreshToken(@Body('refreshToken') refreshToken: string): Promise<RefreshTokenDto> {
    return this.authService.refreshToken(refreshToken);
  }

  @LogoutDoc()
  @UseGuards(JwtAuthGuard)
  @Post('logout')
  @HttpCode(HttpStatus.OK)
  async logout(@Request() req: { user: { id: number } }): Promise<{ message: string }> {
    await this.authService.logout(req.user.id);
    return { message: 'LOGOUT_SUCCESS' };
  }
}
