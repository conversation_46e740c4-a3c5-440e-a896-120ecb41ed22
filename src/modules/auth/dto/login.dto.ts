import { IsNotEmpty, <PERSON>String, <PERSON><PERSON>eng<PERSON> } from 'class-validator';
import { i18nValidationMessage } from 'nestjs-i18n';

export class LoginDto {
  @IsString({ message: i18nValidationMessage('validation.common.mustBeString') })
  @IsNotEmpty({ message: i18nValidationMessage('validation.common.required') })
  username: string;

  @IsString({ message: i18nValidationMessage('validation.common.mustBeString') })
  @IsNotEmpty({ message: i18nValidationMessage('validation.common.required') })
  @MinLength(6, { message: i18nValidationMessage('validation.specific.password.minLength', { min: 6 }) })
  password: string;
}
