import { Injectable } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import * as bcrypt from 'bcryptjs';

import { UsersService } from '@/modules/users/users.service';
import { AuthResponseDto, RefreshTokenDto } from './dto/auth-response.dto';
import { JwtPayload } from './interfaces/jwt-payload.interface';
import { User } from '@/modules/users/entities/user.entity';
import { IAuthService } from './interfaces/auth-service.interface';
import { RegisterDto } from './dto/register.dto';
import { AuthErrors } from './auth.errors';

@Injectable()
export class AuthService implements IAuthService {
  constructor(
    private readonly usersService: UsersService,
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
  ) {}

  async validateUser(username: string, password: string): Promise<User | null> {
    const user = await this.usersService.findByUsername(username);

    if (user && (await user.validatePassword(password)) && user.isActive) {
      return user;
    }

    return null;
  }

  async login(user: User): Promise<AuthResponseDto> {
    const payload: JwtPayload = {
      sub: user?.id,
      username: user?.username,
    };

    const accessToken = this.jwtService.sign(payload);
    const refreshToken = this.jwtService.sign(payload, {
      secret: this.configService.get<string>('jwt.refreshSecret'),
      expiresIn: this.configService.get<string>('jwt.refreshExpiresIn') ?? '7d',
    });

    await this.usersService.updateRefreshToken(user.id, refreshToken);

    return {
      accessToken,
      refreshToken,
      expiresIn: this.configService.get<number>('jwt.expiresIn') ?? 3600,
    };
  }

  async register(registerDto: RegisterDto): Promise<AuthResponseDto> {
    await this.usersService.create(registerDto);
    const user = await this.usersService.findByUsername(registerDto.username);

    if (!user) {
      throw AuthErrors.USER_CREATION_FAILED();
    }

    return this.login(user);
  }

  async refreshToken(refreshToken: string): Promise<RefreshTokenDto> {
    try {
      const payload = this.jwtService.verify(refreshToken, {
        secret: this.configService.get<string>('jwt.refreshSecret'),
      });

      const user = await this.usersService.findOne(Number(payload.sub));
      if (!user) {
        throw AuthErrors.USER_NOT_FOUND();
      }

      const storedUser = await this.usersService.findByUsername(user.username);
      if (!storedUser?.refreshToken || !(await bcrypt.compare(refreshToken, storedUser.refreshToken))) {
        throw AuthErrors.INVALID_REFRESH_TOKEN();
      }

      const newPayload: JwtPayload = {
        username: user.username,
        sub: user.id,
      };

      const accessToken = this.jwtService.sign(newPayload);

      return {
        accessToken,
        expiresIn: this.configService.get<string>('jwt.expiresIn') ?? '24h',
      };
    } catch {
      throw AuthErrors.INVALID_REFRESH_TOKEN();
    }
  }

  async logout(userId: number): Promise<void> {
    await this.usersService.updateRefreshToken(userId, null);
  }
}
