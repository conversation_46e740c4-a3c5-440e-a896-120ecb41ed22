import {
  Entity,
  Column,
  Index,
  OneToMany,
  Tree,
  Check,
  TreeChildren,
  TreeParent,
  BeforeInsert,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { IsNotEmpty, IsOptional, IsUrl, Length } from 'class-validator';
import { Expose } from 'class-transformer';

import { BaseEntity } from '@/common/base/base.entity';
import { Product } from '@/modules/products/entities/product.entity';

@Entity('categories')
@Tree('closure-table')
@Index(['name', 'isActive', 'deletedAt'])
@Check('"name" <> \'\'')
export class Category extends BaseEntity {
  @Expose()
  @PrimaryGeneratedColumn()
  id: number;

  @Expose()
  @Column({
    type: 'varchar',
    length: 100,
    nullable: false,
  })
  @IsNotEmpty()
  @Length(1, 300)
  name: string;

  @Expose()
  @Column({
    type: 'varchar',
    length: 150,
    unique: true,
  })
  slug: string;

  @Expose()
  @Column({
    type: 'text',
    nullable: true,
  })
  @IsOptional()
  description?: string;

  @Expose()
  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
  })
  @IsOptional()
  @IsUrl()
  imageUrl?: string;

  @Expose()
  @Column({
    type: 'boolean',
    default: true,
  })
  isActive: boolean;

  @Expose()
  @Column({
    type: 'integer',
    default: 0,
  })
  sortOrder: number;

  @Column({ nullable: true })
  parentId?: number;

  @Expose()
  @TreeChildren()
  children: Category[];

  @Expose()
  @TreeParent()
  parent?: Category;

  @OneToMany('Product', 'category')
  products: Promise<Product[]>;

  @BeforeInsert()
  slugify(): void {
    if ((!this.slug || this.slug.trim() === '') && this.name) {
      this.slug = this.name
        .normalize('NFD')
        .replace(/[\u0300-\u036f]/g, '')
        .toLowerCase()
        .replace(/[^\w\s-]/g, '')
        .trim()
        .replace(/\s+/g, '-');
    }
  }

  get hasChildren(): boolean {
    return this.children && this.children.length > 0;
  }
}
