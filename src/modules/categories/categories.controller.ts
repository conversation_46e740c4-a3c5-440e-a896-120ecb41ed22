import {
  Controller,
  Get,
  Post,
  Body,
  Query,
  Delete,
  HttpCode,
  HttpStatus,
  Param,
  ParseIntPipe,
  Patch,
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { PaginationDto } from '@/common/dto/pagination.dto';
import { CategoriesService } from './categories.service';
import { Category } from './entities/category.entity';
import { CreateCategoryDto } from './dto/create-category.dto';
import { PaginatedResult } from '@/common/dto/paginated-result.dto';
import { CreateCategoryDoc, DeleteCategoryDoc, GetAllCategoriesDoc, UpdateCategoryDoc } from './docs/categories.docs';
import { UpdateCategoryDto } from './dto/update-category.dto';
import { RestoreEntityDoc } from '@/common/docs/base.docs';
import { TransformResponse } from '@/common/decorators/transform-response.decorator';
@ApiTags('Categories')
@Controller({ path: 'api/categories', version: '1' })
export class CategoriesController {
  constructor(private readonly categoriesService: CategoriesService) {}

  @CreateCategoryDoc()
  @Post()
  @TransformResponse(Category)
  async create(@Body() createCategoryDto: CreateCategoryDto): Promise<Category> {
    return this.categoriesService.create(createCategoryDto);
  }

  @GetAllCategoriesDoc()
  @Get()
  @TransformResponse(Category)
  async findAll(
    @Query('page') page?: number,
    @Query('limit') limit?: number,
  ): Promise<Category[] | PaginatedResult<Category>> {
    if (page || limit) {
      const pagination = new PaginationDto();

      if (page) pagination.page = page;
      if (limit) pagination.limit = limit;

      if (pagination.page || pagination.limit) {
        return this.categoriesService.findAllPaginated(pagination);
      }
    }

    return this.categoriesService.findTrees();
  }

  @UpdateCategoryDoc()
  @Patch(':id')
  @TransformResponse(Category)
  async update(@Param('id', ParseIntPipe) id: number, @Body() updateCategoryDto: UpdateCategoryDto): Promise<Category> {
    return this.categoriesService.update(id, updateCategoryDto);
  }

  @DeleteCategoryDoc()
  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  async remove(@Param('id', ParseIntPipe) id: number): Promise<void> {
    return this.categoriesService.remove(id);
  }

  @RestoreEntityDoc()
  @Patch(':id/restore')
  async restore(@Param('id', ParseIntPipe) id: number): Promise<Category> {
    return this.categoriesService.restore(id);
  }
}
