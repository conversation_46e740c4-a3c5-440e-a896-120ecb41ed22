import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { TreeRepository } from 'typeorm';

import { Category } from './entities/category.entity';
import { CreateCategoryDto } from './dto/create-category.dto';
import { getNextSortOrder } from '@/common/utils/getNextSortOrder.util';
import { PaginationDto } from '@/common/dto/pagination.dto';
import { PaginatedResult } from '@/common/dto/paginated-result.dto';
import { UpdateCategoryDto } from './dto/update-category.dto';
import { CategoryErrors } from './categories.errors';

@Injectable()
export class CategoriesService {
  constructor(
    @InjectRepository(Category)
    private readonly categoryRepository: TreeRepository<Category>,
  ) {}

  async create(createCategoryDto: CreateCategoryDto): Promise<Category> {
    const { parentId, ...categoryData } = createCategoryDto;
    const existingCategory = await this.categoryRepository.findOne({
      where: { name: createCategoryDto.name },
    });

    if (existingCategory) {
      throw CategoryErrors.CATEGORY_ALREADY_EXISTS(createCategoryDto.name);
    }

    const sortOrder = await getNextSortOrder(this.categoryRepository);
    const category = this.categoryRepository.create({
      ...categoryData,
      sortOrder,
    });

    if (parentId) {
      const parent = await this.categoryRepository.findOne({
        where: { id: parentId },
      });

      if (!parent) {
        throw CategoryErrors.CATEGORY_NOT_FOUND(parentId);
      }

      category.parent = parent;
    }
    return this.categoryRepository.save(category);
  }

  async findTrees(): Promise<Category[]> {
    return this.categoryRepository.findTrees();
  }

  async findAllPaginated(pagination: PaginationDto): Promise<PaginatedResult<Category>> {
    const [users, total] = await this.categoryRepository.findAndCount({
      order: { sortOrder: 'ASC' },
      skip: pagination.skip,
      take: pagination.limit,
    });

    return new PaginatedResult(users, total, pagination.page, pagination.limit);
  }

  async update(id: number, updateCategoryDto: UpdateCategoryDto): Promise<Category> {
    if (updateCategoryDto.name) {
      const existingCategory = await this.categoryRepository.findOne({
        where: { name: updateCategoryDto.name },
      });

      if (existingCategory && existingCategory.id !== id) {
        throw CategoryErrors.CATEGORY_ALREADY_EXISTS(updateCategoryDto.name);
      }
    }

    const category = await this.findOne(id);
    Object.assign(category, updateCategoryDto);
    return this.categoryRepository.save(category);
  }

  async remove(id: number): Promise<void> {
    const category = await this.findOne(id);
    await this.categoryRepository.softRemove(category);
  }

  async findOne(id: number): Promise<Category> {
    const category = await this.categoryRepository.findOne({ where: { id } });
    if (!category) {
      throw CategoryErrors.CATEGORY_NOT_FOUND(id);
    }
    return category;
  }

  async restore(id: number): Promise<Category> {
    const category = await this.categoryRepository.findOne({
      where: { id },
      withDeleted: true,
    });

    if (!category) {
      throw CategoryErrors.CATEGORY_NOT_FOUND(id);
    }

    return this.categoryRepository.recover(category);
  }
}
