import { BaseError } from '@/core/errors/base.error';
import { defineErrors } from '@/core/errors/define-errors';
import { HttpStatus } from '@nestjs/common';

export const CategoryErrors = defineErrors('category', {
  CATEGORY_NOT_FOUND: (id: number): BaseError => {
    return new BaseError('category.CATEGORY_NOT_FOUND', { id }, [], HttpStatus.NOT_FOUND);
  },
  CATEGORY_ALREADY_EXISTS: (name: string): BaseError => {
    return new BaseError('category.CATEGORY_ALREADY_EXISTS', { name }, [], HttpStatus.CONFLICT);
  },
});
