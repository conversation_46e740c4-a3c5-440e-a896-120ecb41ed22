import { applyDecorators, HttpStatus } from '@nestjs/common';
import {
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBody,
  ApiBadRequestResponse,
  ApiNotFoundResponse,
  ApiConflictResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { Category } from '../entities/category.entity';
import { CreateCategoryDto } from '../dto/create-category.dto';
import { UpdateCategoryDto } from '../dto/update-category.dto';

export function CreateCategoryDoc(): MethodDecorator {
  return applyDecorators(
    ApiBearerAuth(),
    ApiOperation({ summary: 'Create a new category' }),
    ApiBody({
      type: CreateCategoryDto,
      examples: {
        'create-category': {
          value: {
            name: 'Sữa các loại',
            description: 'Mô tả Sữa các loại',
            parentId: 'uuid-parent-category-id',
            isActive: true,
          },
        },
      },
    }),
    ApiResponse({
      status: HttpStatus.CREATED,
      description: 'Category created successfully',
      type: Category,
    }),
    ApiBadRequestResponse({ description: 'Validation error' }),
    ApiConflictResponse({ description: 'Category name already exists' }),
  );
}

export function GetAllCategoriesDoc(): MethodDecorator {
  return applyDecorators(
    ApiBearerAuth(),
    ApiOperation({ summary: 'Get all categories' }),
    ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number' }),
    ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page' }),
    ApiResponse({
      status: HttpStatus.OK,
      description: 'Category updated successfully',
      type: Category,
      schema: {
        example: {
          id: '6d07ac48-aeab-41dd-9832-270ee38f3994',
          name: 'Rau củ, trái cây',
          description: 'Mô tả Rau củ, trái cây',
          isActive: true,
          sortOrder: 1,
          createdAt: '2025-07-07 07:31:06',
          updatedAt: '2025-07-07 07:31:06',
          parent: null,
        },
      },
    }),
  );
}

export function UpdateCategoryDoc(): MethodDecorator {
  return applyDecorators(
    ApiBearerAuth(),
    ApiOperation({
      summary: 'Update category',
      description: 'Update any category fields including name, description, isActive, sortOrder, etc.',
    }),
    ApiParam({ name: 'id', description: 'Category ID', type: String }),
    ApiBody({
      type: UpdateCategoryDto,
      examples: {
        'update-category': {
          value: {
            name: 'Sữa các loại',
            description: 'Mô tả Sữa các loại',
            parentId: null,
            isActive: true,
          },
        },
      },
    }),
    ApiResponse({
      status: HttpStatus.OK,
      description: 'Category updated successfully',
      type: Category,
    }),
    ApiBadRequestResponse({ description: 'Validation error' }),
    ApiNotFoundResponse({ description: 'Category not found' }),
    ApiConflictResponse({ description: 'Category name already exists' }),
  );
}

export function DeleteCategoryDoc(): MethodDecorator {
  return applyDecorators(
    ApiBearerAuth(),
    ApiOperation({ summary: 'Delete category' }),
    ApiParam({ name: 'id', description: 'Category ID', type: String }),
    ApiResponse({
      status: HttpStatus.NO_CONTENT,
      description: 'Category deleted successfully',
    }),
    ApiNotFoundResponse({ description: 'Category not found' }),
  );
}
