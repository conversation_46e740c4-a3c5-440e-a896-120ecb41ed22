import { IsString, <PERSON><PERSON>ptional, IsBoolean, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsUrl } from 'class-validator';
import { i18nValidationMessage } from 'nestjs-i18n';

export class CreateCategoryDto {
  @IsString()
  @MinLength(2, { message: i18nValidationMessage('validation.specific.category.name.minLength', { min: 2 }) })
  @MaxLength(300, { message: i18nValidationMessage('validation.specific.category.name.maxLength', { max: 300 }) })
  name: string;

  @IsOptional()
  @IsString()
  @MaxLength(1000, {
    message: i18nValidationMessage('validation.specific.category.description.maxLength', { max: 1000 }),
  })
  description?: string;

  @IsOptional()
  @IsUrl()
  imageUrl?: string;

  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @IsOptional()
  @IsInt()
  sortOrder?: number;

  @IsOptional()
  @IsInt()
  parentId?: number;
}
