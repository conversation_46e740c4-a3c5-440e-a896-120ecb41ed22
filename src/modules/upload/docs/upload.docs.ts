import { applyDecorators, HttpStatus } from '@nestjs/common';
import { ApiConsumes, ApiOperation, ApiResponse } from '@nestjs/swagger';

import { FileUploadResponseDto } from '../dto/upload-response.dto';

export function UploadSingleFileDoc(): MethodDecorator {
  return applyDecorators(
    ApiOperation({ summary: 'Upload a single file' }),
    ApiResponse({ status: HttpStatus.CREATED, description: 'File uploaded successfully', type: FileUploadResponseDto }),
    ApiConsumes('multipart/form-data'),
  );
}
