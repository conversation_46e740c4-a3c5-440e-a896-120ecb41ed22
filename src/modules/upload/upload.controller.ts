import { Body, Controller, HttpCode, HttpStatus, Post, UploadedFile, Request, UseInterceptors } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';

import { UploadService } from './services/upload.service';
import { UploadSingleFileDoc } from './docs/upload.docs';
import { FileUploadDto, ImageProcessingDto } from './dto/upload-request.dto';
import { ImageUploadPipe } from './validators/upload-file.validator';
import { UploadErrors } from './upload.errors';
import { FileUploadResponseDto } from './dto/upload-response.dto';
import { FileInterceptor } from '@nestjs/platform-express';
import { TransformResponse } from '@/common/decorators/transform-response.decorator';
import { File } from './entitis/file.entity';

@ApiTags('File Upload')
@Controller({ path: 'api/uploads', version: '1' })
export class UploadController {
  constructor(private readonly uploadService: UploadService) {}

  @UploadSingleFileDoc()
  @Post('single')
  @HttpCode(HttpStatus.CREATED)
  @UseInterceptors(
    FileInterceptor('file', {
      limits: {
        fileSize: 10 * 1024 * 1024, // 10MB
      },
    }),
  )
  @TransformResponse(File)
  async uploadSingleFile(
    @UploadedFile(ImageUploadPipe) file: Express.Multer.File,
    @Body() uploadDto: FileUploadDto,
    @Body() processingDto: ImageProcessingDto,
    @Request() req: { user?: { id: number } },
  ): Promise<FileUploadResponseDto> {
    if (!file) {
      throw UploadErrors.NO_FILE_UPLOADED();
    }

    const IUploadedFileInfo = {
      originalName: file.originalname,
      filename: file.filename,
      mimeType: file.mimetype,
      size: file.size,
      path: file.path,
      buffer: file.buffer,
    };

    let result = await this.uploadService.uploadSingle(IUploadedFileInfo, processingDto, req.user?.id);

    if (uploadDto.alt || uploadDto.description || uploadDto.isPublic !== undefined) {
      result = await this.uploadService.updateFileMetadata(result.id, {
        alt: uploadDto.alt,
        description: uploadDto.description,
        tags: uploadDto.tags,
        isPublic: uploadDto.isPublic,
      });
    }

    return this.uploadService.mapToResponseDto(result);
  }
}
