import { HttpStatus } from '@nestjs/common';

import { BaseError } from '@/core/errors/base.error';
import { defineErrors } from '@/core/errors/define-errors';

export const UploadErrors = defineErrors('upload', {
  NO_FILE_UPLOADED: (): BaseError => {
    return new BaseError('upload.NO_FILE_UPLOADED', {}, [], HttpStatus.BAD_REQUEST);
  },
  FILE_TOO_LARGE: (maxSize: number): BaseError => {
    return new BaseError('upload.FILE_TOO_LARGE', { maxSize }, [], HttpStatus.BAD_REQUEST);
  },
  // INVALID_FILE_TYPE: (fileType: string, allowedTypes: string[]): BaseError => {
  //   const { fileType: string, allowedTypes: string[] } = args;
  //   return new BaseError('upload.INVALID_FILE_TYPE', { fileType, allowedTypes }, [], HttpStatus.BAD_REQUEST);
  // },
  INVALID_FILE_TYPE: (fileType: string): BaseError => {
    return new BaseError('upload.INVALID_FILE_TYPE', { fileType }, [], HttpStatus.BAD_REQUEST);
  },
  TOO_MANY_FILES: (maxFiles: number): BaseError => {
    return new BaseError('upload.TOO_MANY_FILES', { maxFiles }, [], HttpStatus.BAD_REQUEST);
  },
  INVALID_FILENAME: (): BaseError => {
    return new BaseError('upload.INVALID_FILENAME', {}, [], HttpStatus.BAD_REQUEST);
  },
  UPLOAD_FAILED: (): BaseError => {
    return new BaseError('upload.UPLOAD_FAILED', {}, [], HttpStatus.INTERNAL_SERVER_ERROR);
  },
  STORAGE_ERROR: (originalName: string): BaseError => {
    return new BaseError('upload.STORAGE_ERROR', { originalName }, [], HttpStatus.INTERNAL_SERVER_ERROR);
  },
  STORAGE_INIT_ERROR: (message: string): BaseError => {
    return new BaseError('upload.STORAGE_INIT_ERROR', { message }, [], HttpStatus.INTERNAL_SERVER_ERROR);
  },
});
