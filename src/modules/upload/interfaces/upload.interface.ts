import { IFileMetadata, FileStatus, FileType, IProcessedVariants } from '../entitis/file.entity';

export interface IUploadedFileInfo {
  originalName: string;
  filename: string;
  mimeType: string;
  size: number;
  path: string;
  buffer?: Buffer;
}

export interface IImageProcessingOptions {
  resize?: {
    width?: number;
    height?: number;
    fit?: 'cover' | 'contain' | 'fill' | 'inside' | 'outside';
    position?: string;
  };
  format?: 'jpeg' | 'png' | 'webp' | 'gif';
  quality?: number;
  progressive?: boolean;
  optimize?: boolean;
  generateVariants?: boolean;
}

export interface IFileUploadResult {
  id: number;
  originalName: string;
  filename: string;
  mimeType: string;
  fileType: FileType;
  size: number;
  url: string;
  thumbnailUrl?: string;
  mediumUrl?: string;
  largeUrl?: string;
  status: FileStatus;
  metadata?: IFileMetadata;
  processedVariants?: IProcessedVariants;
  uploadedAt: Date;
  alt?: string;
  description?: string;
  tags?: string[];
  isPublic?: boolean;
  lastAccessedAt?: Date;
  downloadCount?: number;
}

export interface IFileStorageOptions {
  destination?: string;
  filename?: (file: IUploadedFileInfo) => string;
  preserveOriginalName?: boolean;
  createDirectories?: boolean;
}

export interface IFileStorageService {
  store(file: IUploadedFileInfo, options?: IFileStorageOptions): Promise<string>;
  // delete(filePath: string): Promise<void>;
  // exists(filePath: string): Promise<boolean>;
  // getUrl(filePath: string): string;
  // getSize(filePath: string): Promise<number>;
  // move(oldPath: string, newPath: string): Promise<void>;
  // copy(sourcePath: string, destinationPath: string): Promise<void>;
}

export interface IImageProcessingOptions {
  resize?: {
    width?: number;
    height?: number;
    fit?: 'cover' | 'contain' | 'fill' | 'inside' | 'outside';
    position?: string;
  };
  format?: 'jpeg' | 'png' | 'webp' | 'gif';
  quality?: number;
  progressive?: boolean;
  optimize?: boolean;
  generateVariants?: boolean;
}
