import { Entity, Column, Index, BeforeInsert, BeforeUpdate, PrimaryGeneratedColumn } from 'typeorm';
import { BaseEntity } from '@/common/base/base.entity';
import { UPLOAD_CONSTANTS } from '@/config/upload.config';
import { Expose } from 'class-transformer';

export enum FileType {
  IMAGE = 'image',
  DOCUMENT = 'document',
  VIDEO = 'video',
  AUDIO = 'audio',
  OTHER = 'other',
}

export enum FileStatus {
  UPLOADING = 'uploading',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  DELETED = 'deleted',
}

export interface IFileMetadata {
  width?: number;
  height?: number;
  format?: string;
  colorSpace?: string;
  hasAlpha?: boolean;
  orientation?: number;
  density?: number;
  compression?: string;
  quality?: number;
}

export interface IProcessedVariants {
  thumbnail?: {
    path: string;
    url: string;
    width: number;
    height: number;
    size: number;
  };
  medium?: {
    path: string;
    url: string;
    width: number;
    height: number;
    size: number;
  };
  large?: {
    path: string;
    url: string;
    width: number;
    height: number;
    size: number;
  };
}

@Entity('files')
@Index(['originalName', 'deletedAt'])
@Index(['mimeType'])
@Index(['fileType'])
@Index(['status'])
@Index(['uploadedBy'])
@Index(['createdAt'])
export class File extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Expose()
  @Column({ type: 'varchar', length: 255 })
  @Index('IDX_FILE_ORIGINAL_NAME')
  originalName: string;

  @Expose()
  @Column({ type: 'varchar', length: 255, unique: true })
  @Index('IDX_FILE_FILENAME', { unique: true })
  filename: string;

  @Expose()
  @Column({ type: 'varchar', length: 100 })
  @Index('IDX_FILE_MIME_TYPE')
  mimeType: string;

  @Expose()
  @Column({ type: 'varchar', length: 20 })
  @Index('IDX_FILE_TYPE')
  fileType: FileType;

  @Expose()
  @Column({ type: 'integer' })
  size: number;

  @Expose()
  @Column({ type: 'varchar', length: 500 })
  path: string;

  @Expose()
  @Column({ type: 'varchar', length: 500 })
  url: string;

  @Column({ type: 'varchar', length: 64, nullable: true })
  @Index('IDX_FILE_HASH')
  hash?: string;

  @Column({ type: 'varchar', length: 20, default: FileStatus.UPLOADING })
  @Index('IDX_FILE_STATUS')
  status: FileStatus;

  @Expose()
  @Column({ type: 'json', nullable: true })
  metadata?: IFileMetadata;

  @Expose()
  @Column({ type: 'json', nullable: true })
  processedVariants?: IProcessedVariants;

  @Column({ type: 'integer', nullable: true })
  @Index('IDX_FILE_UPLOADED_BY')
  uploadedBy?: number;

  @Expose()
  @Column({ type: 'varchar', length: 100, nullable: true })
  alt?: string;

  @Expose()
  @Column({ type: 'text', nullable: true })
  description?: string;

  @Expose()
  @Column({ type: 'json', nullable: true })
  tags?: string[];

  @Column({ type: 'boolean', default: false })
  @Index('IDX_FILE_PUBLIC')
  isPublic: boolean;

  @Expose()
  @Column({ type: 'datetime', nullable: true })
  lastAccessedAt?: Date;

  @Column({ type: 'integer', default: 0 })
  downloadCount: number;

  get isImage(): boolean {
    return this.fileType === FileType.IMAGE;
  }

  get extension(): string {
    return this.originalName.split('.').pop()?.toLowerCase() ?? '';
  }

  get sizeFormatted(): string {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = this.size;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(1)} ${units[unitIndex]}`;
  }

  get thumbnailUrl(): string | null {
    return this.processedVariants?.thumbnail?.url ?? null;
  }

  get mediumUrl(): string | null {
    return this.processedVariants?.medium?.url ?? null;
  }

  get largeUrl(): string | null {
    return this.processedVariants?.large?.url ?? null;
  }

  @BeforeInsert()
  @BeforeUpdate()
  validateFile(): void {
    // Validate file size
    if (this.size > UPLOAD_CONSTANTS.MAX_FILE_SIZE) {
      throw new Error(`File size exceeds maximum allowed size of ${UPLOAD_CONSTANTS.MAX_FILE_SIZE} bytes`);
    }

    // Validate filename length
    if (this.originalName.length > UPLOAD_CONSTANTS.FILENAME_MAX_LENGTH) {
      throw new Error(`Filename exceeds maximum length of ${UPLOAD_CONSTANTS.FILENAME_MAX_LENGTH} characters`);
    }

    // Set file type based on mime type
    if (!this.fileType) {
      this.fileType = this.determineFileType();
    }

    this.lastAccessedAt ??= new Date();
  }

  private determineFileType(): FileType {
    if (this.mimeType.startsWith('image/')) {
      return FileType.IMAGE;
    } else if (this.mimeType.startsWith('video/')) {
      return FileType.VIDEO;
    } else if (this.mimeType.startsWith('audio/')) {
      return FileType.AUDIO;
    } else if (this.mimeType.includes('pdf') || this.mimeType.includes('document') || this.mimeType.includes('text')) {
      return FileType.DOCUMENT;
    }
    return FileType.OTHER;
  }

  // Helper method to update access tracking
  updateAccessTracking(): void {
    this.lastAccessedAt = new Date();
    this.downloadCount += 1;
  }

  // Helper method to check if file processing is complete
  isProcessingComplete(): boolean {
    return this.status === FileStatus.COMPLETED;
  }

  // Helper method to check if file has variants
  hasProcessedVariants(): boolean {
    return !!(this.processedVariants && Object.keys(this.processedVariants).length > 0);
  }
}
