import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { createHash } from 'crypto';
import { Repository } from 'typeorm';
import { extname, basename } from 'path';

import { UploadConfig } from '@/config/upload.config';
import { IFileUploadResult, IImageProcessingOptions, IUploadedFileInfo } from '../interfaces/upload.interface';
import { FileStorageService } from './file-storage.service';
import { UploadErrors } from '../upload.errors';
import { File, FileStatus } from '../entitis/file.entity';
import { ImageProcessingService } from './image-processing.service';
import { FileUploadResponseDto } from '../dto/upload-response.dto';

@Injectable()
export class UploadService {
  private readonly logger = new Logger(UploadService.name);
  private readonly uploadConfig: UploadConfig;

  constructor(
    @InjectRepository(File)
    private readonly fileRepository: Repository<File>,
    private readonly configService: ConfigService,
    private readonly fileStorageService: FileStorageService,
    private readonly imageProcessingService: ImageProcessingService,
  ) {
    this.uploadConfig = this.configService.get<UploadConfig>('upload') as UploadConfig;
  }

  async uploadSingle(
    file: IUploadedFileInfo,
    options?: IImageProcessingOptions,
    uploadedBy?: number,
  ): Promise<IFileUploadResult> {
    try {
      this.logger.log(`Starting single file upload: ${file.originalName}`);

      // Check for duplicates if enabled
      if (this.uploadConfig.security.preventDuplicates && file.buffer) {
        const hash = this.calculateFileHash(file.buffer);
        const existingFile = await this.fileRepository.findOne({ where: { hash } });
        if (existingFile) {
          throw UploadErrors.FILE_ALREADY_EXISTS();
        }
      }

      // Generate unique filename
      const filename = this.generateUniqueFilename(file.originalName);
      const fileWithNewName = { ...file, filename };

      // Store file
      const storedPath = await this.fileStorageService.store(fileWithNewName);
      const fileEntity = this.fileRepository.create({
        originalName: file.originalName,
        filename,
        mimeType: file.mimeType,
        size: file.size,
        path: storedPath,
        url: this.fileStorageService.getUrl(storedPath),
        hash: file.buffer ? this.calculateFileHash(file.buffer) : undefined,
        status: FileStatus.UPLOADING,
        uploadedBy,
      });

      const savedFile = await this.fileRepository.save(fileEntity);

      if (this.isImageFile(file.mimeType)) {
        await this.processImageFile(savedFile, options);
      } else {
        savedFile.status = FileStatus.COMPLETED;
        await this.fileRepository.save(savedFile);
      }

      this.logger.log(`File upload completed: ${savedFile.id}`);
      return this.mapToFileUploadResult(savedFile);
    } catch (error) {
      this.logger.error(`File upload failed for ${file.originalName}:`, error);
      throw error;
    }
  }

  async updateFileMetadata(
    id: number,
    metadata: Partial<Pick<File, 'alt' | 'description' | 'tags' | 'isPublic'>>,
  ): Promise<IFileUploadResult> {
    const file = await this.fileRepository.findOne({ where: { id } });
    if (!file) {
      throw UploadErrors.FILE_NOT_FOUND();
    }

    Object.assign(file, metadata);
    const updatedFile = await this.fileRepository.save(file);

    return this.mapToFileUploadResult(updatedFile);
  }

  mapToResponseDto(result: IFileUploadResult): FileUploadResponseDto {
    return {
      id: result.id,
      originalName: result.originalName,
      filename: result.filename,
      mimeType: result.mimeType,
      fileType: result.fileType,
      size: result.size,
      sizeFormatted: result.size.toString(),
      url: result.url,
      status: result.status,
      metadata: result.metadata,
      processedVariants: result.processedVariants,
      thumbnailUrl: result.thumbnailUrl,
      mediumUrl: result.mediumUrl,
      largeUrl: result.largeUrl,
      alt: result.alt,
      description: result.description,
      tags: result.tags,
      isPublic: result.isPublic ?? false,
      createdAt: result.uploadedAt,
      updatedAt: result.uploadedAt,
      lastAccessedAt: result.lastAccessedAt,
      downloadCount: result.downloadCount ?? 0,
    };
  }

  private calculateFileHash(buffer: Buffer): string {
    return createHash('sha256').update(buffer).digest('hex');
  }

  private generateUniqueFilename(originalName: string): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    const extension = extname(originalName);
    const nameWithoutExt = basename(originalName, extension);
    const sanitizedName = nameWithoutExt
      .replace(/[^a-zA-Z0-9.-]/g, '_')
      .replace(/_+/g, '_')
      .replace(/^_+|_+$/g, '');

    return `${sanitizedName}_${timestamp}_${random}${extension}`;
  }

  private isImageFile(mimeType: string): boolean {
    const imageMimeTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'];
    return imageMimeTypes.includes(mimeType);
  }

  private async processImageFile(file: File, options?: IImageProcessingOptions): Promise<void> {
    try {
      file.status = FileStatus.PROCESSING;
      await this.fileRepository.save(file);

      // Get image metadata
      const metadata = await this.imageProcessingService.getImageMetadata(file.path);
      file.metadata = metadata;

      // Process image variants
      const processedVariants = await this.imageProcessingService.processImage(file.path, options ?? {});
      file.processedVariants = processedVariants;

      file.status = FileStatus.COMPLETED;
      await this.fileRepository.save(file);

      this.logger.log(`Image processing completed for file: ${file.id}`);
    } catch (error) {
      this.logger.error(`Image processing failed for file ${file.id}:`, error);
      file.status = FileStatus.FAILED;
      await this.fileRepository.save(file);
      throw error;
    }
  }

  private mapToFileUploadResult(file: File): IFileUploadResult {
    return {
      id: file.id,
      originalName: file.originalName,
      filename: file.filename,
      mimeType: file.mimeType,
      fileType: file.fileType,
      size: file.size,
      url: file.url,
      thumbnailUrl: file.thumbnailUrl ?? undefined,
      mediumUrl: file.mediumUrl ?? undefined,
      largeUrl: file.largeUrl ?? undefined,
      status: file.status,
      metadata: file.metadata,
      processedVariants: file.processedVariants,
      uploadedAt: file.createdAt,
      alt: file.alt,
      description: file.description,
      tags: file.tags,
      isPublic: file.isPublic,
      lastAccessedAt: file.lastAccessedAt,
      downloadCount: file.downloadCount,
    };
  }
}
