import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { promises as fs } from 'fs';
import { join, dirname, basename, extname } from 'path';
import { UploadConfig } from '@/config/upload.config';
import { IUploadedFileInfo, IFileStorageOptions, IFileStorageService } from '../interfaces/upload.interface';
import { UploadErrors } from '../upload.errors';

@Injectable()
export class FileStorageService implements IFileStorageService {
  private readonly logger = new Logger(FileStorageService.name);
  private readonly uploadConfig: UploadConfig;

  constructor(private readonly configService: ConfigService) {
    this.uploadConfig = this.configService.get<UploadConfig>('upload') as UploadConfig;
    void this.initializeStorage();
  }

  async store(file: IUploadedFileInfo, options?: IFileStorageOptions): Promise<string> {
    try {
      const destination: string = options?.destination ?? this.uploadConfig.storage.destination;
      const filename: string = options?.filename ? options.filename(file) : this.generateFilename(file, options);
      const filePath = join(destination, filename);

      // Ensure destination directory exists
      if (options?.createDirectories !== false) {
        await this.ensureDirectoryExists(dirname(filePath));
      }

      // Write file to storage
      if (file.buffer) {
        await fs.writeFile(filePath, file.buffer);
      } else if (file.path) {
        await fs.copyFile(file.path, filePath);
      } else {
        throw new Error('File must have either buffer or path');
      }

      this.logger.log(`File stored successfully: ${filePath}`);
      return filePath;
    } catch (error) {
      this.logger.error(`Failed to store file ${file.originalName}:`, error);
      throw UploadErrors.STORAGE_ERROR(file.originalName);
    }
  }

  getUrl(filePath: string): string {
    const relativePath = filePath.replace(this.uploadConfig.storage.destination, '');
    return `${this.uploadConfig.storage.publicPath}${relativePath}`.replace(/\\/g, '/');
  }

  private async initializeStorage(): Promise<void> {
    try {
      await this.ensureDirectoryExists(this.uploadConfig.storage.destination);
      await this.ensureDirectoryExists(join(this.uploadConfig.storage.destination, 'thumbnails'));
      await this.ensureDirectoryExists(join(this.uploadConfig.storage.destination, 'medium'));
      await this.ensureDirectoryExists(join(this.uploadConfig.storage.destination, 'large'));
      this.logger.log('Storage directories initialized');
    } catch (error) {
      this.logger.error('Failed to initialize storage:', error);
      throw UploadErrors.STORAGE_INIT_ERROR(`${error.message}`);
    }
  }

  private async ensureDirectoryExists(dirPath: string): Promise<void> {
    try {
      await fs.access(dirPath);
    } catch {
      await fs.mkdir(dirPath, { recursive: true });
    }
  }

  private generateFilename(file: IUploadedFileInfo, options?: IFileStorageOptions): string {
    if (options?.preserveOriginalName) {
      return file.originalName;
    }

    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    const extension = extname(file.originalName);
    const nameWithoutExt = basename(file.originalName, extension);

    // Sanitize the name
    const sanitizedName = nameWithoutExt
      .replace(/[^a-zA-Z0-9.-]/g, '_')
      .replace(/_+/g, '_')
      .replace(/^_+|_+$/g, '');

    return `${sanitizedName}_${timestamp}_${random}${extension}`;
  }
}
