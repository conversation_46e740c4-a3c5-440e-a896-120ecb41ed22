import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { join, dirname, basename, extname } from 'path';
import { promises as fs } from 'fs';

// Dynamic import for sharp to handle packaged environments
let sharp: any = null;

async function loadSharp(): Promise<any> {
  if (sharp === null) {
    try {
      const sharpModule = await import('sharp');
      sharp = sharpModule.default || sharpModule;
    } catch (error) {
      console.warn('Sharp module not available:', error.message);
      sharp = false; // Mark as failed to avoid retrying
    }
  }
  return sharp;
}

import { UploadConfig } from '@/config/upload.config';
import { IFileMetadata, IProcessedVariants } from '../entitis/file.entity';
import { IImageProcessingOptions } from '../interfaces/upload.interface';

@Injectable()
export class ImageProcessingService {
  private readonly logger = new Logger(ImageProcessingService.name);
  private readonly uploadConfig: UploadConfig;

  constructor(private readonly configService: ConfigService) {
    this.uploadConfig = this.configService.get<UploadConfig>('upload') as UploadConfig;
  }

  async processImage(filePath: string, options: IImageProcessingOptions): Promise<IProcessedVariants> {
    try {
      const sharpInstance = await loadSharp();
      if (!sharpInstance) {
        this.logger.warn('Sharp module not available - skipping image processing');
        return {};
      }

      this.logger.log(`Processing image: ${filePath}`);

      const variants: IProcessedVariants = {};
      const baseDir = dirname(filePath);
      const fileName = basename(filePath, extname(filePath));
      const outputFormat = options.format ?? 'jpeg';

      // Generate variants if enabled
      if (options.generateVariants !== false) {
        // Generate thumbnail
        const thumbnailPath = await this.generateVariant(
          filePath,
          join(baseDir, 'thumbnails', `${fileName}-thumb.${outputFormat}`),
          this.uploadConfig.imageProcessing.sizes.thumbnail.width,
          this.uploadConfig.imageProcessing.sizes.thumbnail.height,
          this.uploadConfig.imageProcessing.sizes.thumbnail.quality,
          options,
        );

        if (thumbnailPath) {
          const thumbnailStats = await fs.stat(thumbnailPath);
          const thumbnailMetadata = await this.getImageMetadata(thumbnailPath);
          variants.thumbnail = {
            path: thumbnailPath,
            url: this.getPublicUrl(thumbnailPath),
            width: thumbnailMetadata.width ?? this.uploadConfig.imageProcessing.sizes.thumbnail.width,
            height: thumbnailMetadata.height ?? this.uploadConfig.imageProcessing.sizes.thumbnail.height,
            size: thumbnailStats.size,
          };
        }

        // Generate medium variant
        const mediumPath = await this.generateVariant(
          filePath,
          join(baseDir, 'medium', `${fileName}-medium.${outputFormat}`),
          this.uploadConfig.imageProcessing.sizes.medium.width,
          this.uploadConfig.imageProcessing.sizes.medium.height,
          this.uploadConfig.imageProcessing.sizes.medium.quality,
          options,
        );

        if (mediumPath) {
          const mediumStats = await fs.stat(mediumPath);
          const mediumMetadata = await this.getImageMetadata(mediumPath);
          variants.medium = {
            path: mediumPath,
            url: this.getPublicUrl(mediumPath),
            width: mediumMetadata.width ?? this.uploadConfig.imageProcessing.sizes.medium.width,
            height: mediumMetadata.height ?? this.uploadConfig.imageProcessing.sizes.medium.height,
            size: mediumStats.size,
          };
        }

        // Generate large variant
        const largePath = await this.generateVariant(
          filePath,
          join(baseDir, 'large', `${fileName}-large.${outputFormat}`),
          this.uploadConfig.imageProcessing.sizes.large.width,
          this.uploadConfig.imageProcessing.sizes.large.height,
          this.uploadConfig.imageProcessing.sizes.large.quality,
          options,
        );

        if (largePath) {
          const largeStats = await fs.stat(largePath);
          const largeMetadata = await this.getImageMetadata(largePath);
          variants.large = {
            path: largePath,
            url: this.getPublicUrl(largePath),
            width: largeMetadata.width ?? this.uploadConfig.imageProcessing.sizes.large.width,
            height: largeMetadata.height ?? this.uploadConfig.imageProcessing.sizes.large.height,
            size: largeStats.size,
          };
        }
      }

      this.logger.log(`Image processing completed: ${Object.keys(variants).length} variants generated`);
      return variants;
    } catch (error) {
      this.logger.error(`Image processing failed for ${filePath}:`, error);
      throw new Error(`Image processing failed: ${error.message}`);
    }
  }

  private async generateVariant(
    inputPath: string,
    outputPath: string,
    width: number,
    height: number,
    quality: number,
    options: IImageProcessingOptions,
  ): Promise<string | null> {
    try {
      const sharpInstance = await loadSharp();
      if (!sharpInstance) {
        this.logger.warn('Sharp module not available - skipping variant generation');
        return null;
      }

      await this.ensureDirectoryExists(dirname(outputPath));

      const image = sharpInstance(inputPath);
      const metadata = await image.metadata();

      // Skip if original is smaller than target size
      if (metadata.width && metadata.height && metadata.width <= width && metadata.height <= height) {
        return null;
      }

      let processedImage = image.resize(width, height, {
        fit: options.resize?.fit ?? 'cover',
        position: options.resize?.position ?? 'center',
      });

      // Apply format-specific options
      const outputFormat = options.format ?? 'jpeg';
      switch (outputFormat) {
        case 'jpeg':
          processedImage = processedImage.jpeg({
            quality,
            progressive: options.progressive ?? false,
          });
          break;
        case 'png':
          processedImage = processedImage.png();
          break;
        case 'webp':
          processedImage = processedImage.webp({ quality });
          break;
        case 'gif':
          processedImage = processedImage.gif();
          break;
      }

      await processedImage.toFile(outputPath);
      return outputPath;
    } catch (error) {
      this.logger.error(`Failed to generate variant ${outputPath}:`, error);
      return null;
    }
  }

  private async ensureDirectoryExists(dirPath: string): Promise<void> {
    try {
      await fs.access(dirPath);
    } catch {
      await fs.mkdir(dirPath, { recursive: true });
    }
  }

  async getImageMetadata(filePath: string): Promise<IFileMetadata> {
    try {
      const sharpInstance = await loadSharp();
      if (!sharpInstance) {
        this.logger.warn('Sharp module not available - cannot get image metadata');
        return {};
      }

      const metadata = await sharpInstance(filePath).metadata();

      return {
        width: metadata.width,
        height: metadata.height,
        format: metadata.format,
        colorSpace: metadata.space,
        hasAlpha: metadata.hasAlpha,
        orientation: metadata.orientation,
        density: metadata.density,
        compression: metadata.compression,
      };
    } catch (error) {
      this.logger.error(`Failed to get image metadata for ${filePath}:`, error);
      return {};
    }
  }

  private getPublicUrl(filePath: string): string {
    const relativePath = filePath.replace(this.uploadConfig.storage.destination, '');
    return `${this.uploadConfig.storage.publicPath}${relativePath}`.replace(/\\/g, '/');
  }
}
