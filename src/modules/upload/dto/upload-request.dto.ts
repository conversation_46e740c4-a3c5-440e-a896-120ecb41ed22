import { IsOptional, IsString, IsBoolean, IsNumber, IsArray, IsEnum, Min, Max, Max<PERSON>ength } from 'class-validator';
import { Transform } from 'class-transformer';

export class FileUploadDto {
  @IsOptional()
  @IsString()
  @MaxLength(255)
  alt?: string;

  @IsOptional()
  @IsString()
  @MaxLength(1000)
  description?: string;

  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  isPublic?: boolean;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];
}

export class ImageProcessingDto {
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(4000)
  width?: number;

  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(4000)
  @Transform(({ value }) => parseInt(value, 10))
  height?: number;

  @IsOptional()
  @IsEnum(['jpeg', 'png', 'webp', 'gif'])
  format?: 'jpeg' | 'png' | 'webp' | 'gif';

  @IsOptional()
  @IsEnum(['cover', 'contain', 'fill', 'inside', 'outside'])
  fit?: 'cover' | 'contain' | 'fill' | 'inside' | 'outside';

  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  generateVariants?: boolean;

  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  optimize?: boolean;

  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  progressive?: boolean;
}

export class SingleFileUploadDto extends FileUploadDto {
  file: Express.Multer.File;
}

export class MultipleFileUploadDto extends FileUploadDto {
  files: Express.Multer.File[];
}

export class FileSearchDto {
  @IsOptional()
  @IsEnum(['image', 'document', 'video', 'audio', 'other'])
  fileType?: string;

  @IsOptional()
  @IsString()
  mimeType?: string;

  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseInt(value, 10))
  uploadedBy?: number;

  @IsOptional()
  @IsEnum(['uploading', 'processing', 'completed', 'failed', 'deleted'])
  status?: string;

  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  isPublic?: boolean;

  @IsOptional()
  @IsString()
  @Transform(({ value }) => value.split(',').map((tag: string) => tag.trim()))
  tags?: string;

  @IsOptional()
  @IsString()
  @Transform(({ value }) => new Date(value))
  dateFrom?: string;

  @IsOptional()
  @IsString()
  @Transform(({ value }) => new Date(value))
  dateTo?: string;

  @IsOptional()
  @IsNumber()
  @Min(0)
  @Transform(({ value }) => parseInt(value, 10))
  sizeMin?: number;

  @IsOptional()
  @IsNumber()
  @Min(1)
  @Transform(({ value }) => parseInt(value, 10))
  sizeMax?: number;
}

export class UpdateFileDto {
  @IsOptional()
  @IsString()
  @MaxLength(255)
  alt?: string;

  @IsOptional()
  @IsString()
  @MaxLength(1000)
  description?: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @IsOptional()
  @IsBoolean()
  isPublic?: boolean;
}
