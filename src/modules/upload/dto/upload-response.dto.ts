import { Expose, Transform } from 'class-transformer';
import { FileType, FileStatus } from '@/modules/upload/entitis/file.entity';
export class FileVariantDto {
  @Expose()
  path: string;

  @Expose()
  url: string;

  @Expose()
  width: number;

  @Expose()
  height: number;

  @Expose()
  size: number;
}

export class ProcessedVariantsDto {
  @Expose()
  thumbnail?: FileVariantDto;

  @Expose()
  medium?: FileVariantDto;

  @Expose()
  large?: FileVariantDto;
}

export class FileMetadataDto {
  @Expose()
  width?: number;

  @Expose()
  height?: number;

  @Expose()
  format?: string;

  @Expose()
  orientation?: number;

  @Expose()
  compression?: string;

  @Expose()
  quality?: number;
}

export class FileUploadResponseDto {
  @Expose()
  id: number;

  @Expose()
  originalName: string;

  @Expose()
  filename: string;

  @Expose()
  mimeType: string;

  @Expose()
  fileType: FileType;

  @Expose()
  size: number;

  @Expose()
  @Transform(({ obj }) => obj.sizeFormatted)
  sizeFormatted: string;

  @Expose()
  url: string;

  @Expose()
  status: FileStatus;

  @Expose()
  metadata?: FileMetadataDto;

  @Expose()
  processedVariants?: ProcessedVariantsDto;

  @Expose()
  @Transform(({ obj }) => obj.thumbnailUrl)
  thumbnailUrl?: string;

  @Expose()
  @Transform(({ obj }) => obj.mediumUrl)
  mediumUrl?: string;

  @Expose()
  @Transform(({ obj }) => obj.largeUrl)
  largeUrl?: string;

  @Expose()
  alt?: string;

  @Expose()
  description?: string;

  @Expose()
  tags?: string[];

  @Expose()
  isPublic: boolean;

  @Expose()
  createdAt: Date;

  @Expose()
  updatedAt: Date;

  @Expose()
  lastAccessedAt?: Date;

  @Expose()
  downloadCount: number;
}

export class FailedUploadDto {
  @Expose()
  originalName: string;

  @Expose()
  error: string;

  @Expose()
  code?: string;
}

export class MultipleFileUploadResponseDto {
  @Expose()
  successful: FileUploadResponseDto[];

  @Expose()
  failed: FailedUploadDto[];

  @Expose()
  totalUploaded: number;

  @Expose()
  totalFailed: number;
}

export class FileStatisticsDto {
  @Expose()
  totalFiles: number;

  @Expose()
  totalSize: number;

  @Expose()
  filesByType: Record<FileType, number>;

  @Expose()
  filesByStatus: Record<FileStatus, number>;

  @Expose()
  averageFileSize: number;

  @Expose()
  mostUploadedMimeTypes: Array<{ mimeType: string; count: number }>;

  @Expose()
  uploadTrends: Array<{ date: string; count: number; size: number }>;
}
