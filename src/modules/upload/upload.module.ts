import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { MulterModule } from '@nestjs/platform-express';
import { diskStorage } from 'multer';
import { extname, join } from 'path';
import { existsSync, mkdirSync } from 'fs';

import { uploadConfig } from '@/config/upload.config';
import { UploadController } from './upload.controller';
import { UploadService } from './services/upload.service';
import { ImageProcessingService } from './services/image-processing.service';
import { FileStorageService } from './services/file-storage.service';
import { File } from './entitis/file.entity';

@Module({
  imports: [
    ConfigModule.forFeature(uploadConfig),
    TypeOrmModule.forFeature([File]),
    MulterModule.registerAsync({
      imports: [ConfigModule],
      useFactory: () => {
        const uploadPath = process.env.UPLOAD_DESTINATION ?? join(process.cwd(), 'uploads');
        if (!existsSync(uploadPath)) {
          mkdirSync(uploadPath, { recursive: true });
        }

        return {
          storage: diskStorage({
            destination: (req, file, cb) => {
              cb(null, uploadPath);
            },
            filename: (req, file, cb) => {
              const timestamp = Date.now();
              const random = Math.random().toString(36).substring(2, 8);
              const extension = extname(file.originalname);
              const nameWithoutExt = file.originalname.replace(extension, '');
              const sanitizedName = nameWithoutExt
                .replace(/[^a-zA-Z0-9.-]/g, '_')
                .replace(/_+/g, '_')
                .replace(/^_+|_+$/g, '');

              const filename = `${sanitizedName}_${timestamp}_${random}${extension}`;
              cb(null, filename);
            },
          }),
          limits: {
            fileSize: parseInt(process.env.UPLOAD_MAX_FILE_SIZE ?? '10485760', 10), // 10MB default
            files: parseInt(process.env.UPLOAD_MAX_FILES ?? '10', 10),
          },
          fileFilter: (req, file, cb): void => {
            const allowedMimeTypes = [
              'image/jpeg',
              'image/jpg',
              'image/png',
              'image/gif',
              'image/webp',
              'image/svg+xml',
            ];

            if (allowedMimeTypes.includes(file.mimetype)) {
              cb(null, true);
            } else {
              cb(new Error(`File type ${file.mimetype} is not allowed`), false);
            }
          },
        };
      },
    }),
  ],
  controllers: [UploadController],
  providers: [UploadService, ImageProcessingService, FileStorageService],
  exports: [UploadService, ImageProcessingService, FileStorageService],
})
export class UploadModule {}
