import { Injectable, Logger, OnApplicationBootstrap } from '@nestjs/common';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { MiniSearchService } from './minisearch.service';
import { Product } from '@/modules/products/entities/product.entity';
import { Category } from '@/modules/categories/entities/category.entity';
import { ProductIndexingResult, SearchableProduct } from '../interfaces/product.interface';
import { ConfigService } from '@nestjs/config';
import { AutoImportService } from '@/modules/product-import/services/auto-import.service';
import { PaginatedResult } from '@/common/dto/paginated-result.dto';
import { SearchQuery } from '../interfaces/search.interface';
import { SearchRequestDto } from '../dto/search-request.dto';
import { ProductFilterDto } from '../dto/product-filter.dto';

@Injectable()
export class ProductSearchService implements OnApplicationBootstrap {
  private readonly logger = new Logger(ProductSearchService.name);
  private readonly collectionAlias = 'products';

  constructor(
    private readonly miniSearchService: MiniSearchService,
    private readonly configService: ConfigService,
    private readonly autoImportService: AutoImportService,
    @InjectRepository(Product)
    private readonly productRepo: Repository<Product>,
    @InjectRepository(Category)
    private readonly categoryRepo: Repository<Category>,
  ) {}

  async onApplicationBootstrap(): Promise<void> {
    try {
      this.logger.debug('Initializing Product Search Module...');
      this.validateConfiguration();
      this.setupScheduledTasks();

      // Wait for auto-import to complete before indexing
      await this.waitForAutoImportCompletion();
      await this.autoIndexExistingProducts();
      this.checkMiniSearchConnection();
      this.logger.log('Search Module initialized successfully');
    } catch (error) {
      this.logger.warn('Search Module initialization failed. Search functionality will be limited.', error.message);
    }
  }

  async indexProduct(product: Product): Promise<void> {
    try {
      const searchableProduct = await this.convertToSearchableProduct(product);
      this.miniSearchService.indexDocument(this.collectionAlias, searchableProduct, {
        action: 'upsert',
      });
      this.logger.debug(`Product indexed: ${product.id}`);
    } catch (error) {
      throw new Error(`Failed to index product: ${product.id}`, { cause: error });
    }
  }

  removeProduct(productId: string): void {
    try {
      this.miniSearchService.removeDocument(this.collectionAlias, productId);
      this.logger.debug(`Product removed from index: ${productId}`);
    } catch (error) {
      throw new Error(`Failed to remove product from index: ${productId}`, { cause: error });
    }
  }

  searchProducts(searchRequest: SearchRequestDto): PaginatedResult<Product> {
    try {
      const query = this.buildSearchQuery(searchRequest);
      const result = this.miniSearchService.search<Product>(this.collectionAlias, query);
      const products = result.hits.map(hit => hit.document);
      const total = result.found;
      const page: number = searchRequest.page ?? 1;
      const perPage: number = searchRequest.limit ?? 20;

      return new PaginatedResult(products, total, page, perPage);
    } catch (error) {
      throw new Error(`Product search failed: ${error.message}`, { cause: error });
    }
  }

  private async convertToSearchableProduct(product: Product): Promise<SearchableProduct> {
    const category = await this.categoryRepo.findOne({
      where: { id: product.categoryId },
      withDeleted: false,
    });

    const categoryPath = category ? [category.name] : [];

    const searchKeywords = [product.name, product.id, category?.name ?? 'Uncategorized'];

    if (product.description) {
      searchKeywords.push(product.description);
    }

    return {
      id: product.id.toString(),
      name: product.name,
      description: product.description ?? '',
      price: Number(product.price) || 0,
      tax: 0,
      categoryId: product.categoryId.toString(),
      categoryName: category?.name ?? 'Uncategorized',
      categoryPath,
      imageUrl: product.imageUrl ?? '',
      isActive: product.isActive,
      createdAt:
        product.createdAt instanceof Date
          ? Math.floor(product.createdAt.getTime() / 1000)
          : Math.floor(Date.now() / 1000),
      updatedAt:
        product.updatedAt instanceof Date
          ? Math.floor(product.updatedAt.getTime() / 1000)
          : Math.floor(Date.now() / 1000),
      searchKeywords,
    };
  }

  private validateConfiguration(): void {
    const config = this.configService.get('minisearch');

    if (!config) {
      throw new Error('MiniSearch configuration not found');
    }

    if (!config.fields?.searchableFields || config.fields.searchableFields.length === 0) {
      throw new Error('MiniSearch searchable fields configuration is required');
    }

    if (!config.fields.storeFields || config.fields.storeFields.length === 0) {
      throw new Error('MiniSearch store fields configuration is required');
    }

    this.logger.log('MiniSearch configuration validated');
  }

  private checkMiniSearchConnection(): void {
    try {
      if (!this.miniSearchService.isReady()) {
        throw new Error('MiniSearch service is not ready');
      }

      const health = this.miniSearchService.getHealthStatus();
      this.logger.log('MiniSearch connection established', { health });
    } catch (error) {
      this.logger.error('MiniSearch connection failed', error);
      throw new Error(`MiniSearch connection failed: ${error.message}`);
    }
  }

  private setupScheduledTasks(): void {
    const config = this.configService.get('minisearch');

    if (config?.indexing?.enableAutoReindex) {
      this.logger.log('Auto-reindexing is enabled');
    }

    if (config?.indexing?.enableIncrementalSync) {
      this.logger.log('Incremental sync is enabled');
    }

    if (config?.performance?.enableCaching) {
      this.logger.log('Search caching is enabled');
    }
  }

  private async waitForAutoImportCompletion(): Promise<void> {
    try {
      this.logger.debug('Waiting for auto-import to complete...');
      await this.autoImportService.waitForBootstrapCompletion();
      this.logger.debug('Auto-import completed - proceeding with indexing');
    } catch (error) {
      this.logger.warn('Auto-import failed, but proceeding with indexing', error.message);
    }
  }

  private async autoIndexExistingProducts(): Promise<void> {
    try {
      this.logger.debug('Starting automatic indexing of existing products...');
      const result = await this.reindexAllProducts();
      this.logger.log(`Auto-indexing completed: ${result.indexed} products indexed, ${result.failed} failed`);
    } catch (error) {
      this.logger.warn('Auto-indexing failed, but application will continue', error.message);
    }
  }

  async reindexAllProducts(): Promise<ProductIndexingResult> {
    try {
      const startTime = Date.now();
      this.logger.log('Starting full product reindexing...');

      const products = await this.productRepo.find({
        where: { isActive: true },
        withDeleted: false,
      });

      const batchSize = 100;
      let indexed = 0;
      let failed = 0;
      const errors: any[] = [];

      for (let i = 0; i < products.length; i += batchSize) {
        const batch = products.slice(i, i + batchSize);
        const searchableProducts: SearchableProduct[] = [];

        for (const product of batch) {
          try {
            const searchableProduct = await this.convertToSearchableProduct(product);
            searchableProducts.push(searchableProduct);
          } catch (error) {
            failed++;
            errors.push({
              productId: product.id,
              error: error.message,
            });
          }
        }

        // Index the batch
        if (searchableProducts.length > 0) {
          try {
            this.miniSearchService.bulkIndexDocuments(this.collectionAlias, searchableProducts, {
              action: 'upsert',
              batch_size: batchSize,
            });
            indexed += searchableProducts.length;
            this.logger.debug(
              `Indexed batch: ${i + 1}-${Math.min(i + batchSize, products.length)} of ${products.length}`,
            );
          } catch (error) {
            failed += searchableProducts.length;
            searchableProducts.forEach(product => {
              errors.push({
                productId: product.id,
                error: error.message,
              });
            });
          }
        }
      }

      const duration = Date.now() - startTime;
      this.logger.log(`Product reindexing completed: ${indexed} indexed, ${failed} failed in ${duration}ms`);

      return {
        success: failed === 0,
        indexed,
        failed,
        errors,
        duration,
      };
    } catch (error) {
      throw new Error(`Product reindexing failed: ${error.message}`, { cause: error });
    }
  }

  private buildSearchQuery(searchRequest: SearchRequestDto, filters?: ProductFilterDto): SearchQuery {
    const query: SearchQuery = {
      q: searchRequest.q ?? '*',
      page: searchRequest.page ?? 1,
      limit: Math.min(searchRequest.limit ?? 10, 100),
    };

    if (filters) {
      const filterParts: string[] = [];

      if (filters.categoryIds && filters.categoryIds.length > 0) {
        filterParts.push(`categoryId:[${filters.categoryIds.join(',')}]`);
      }

      if (filters.priceRange) {
        if (filters.priceRange.min !== undefined) {
          filterParts.push(`price:>=${filters.priceRange.min}`);
        }
        if (filters.priceRange.max !== undefined) {
          filterParts.push(`price:<=${filters.priceRange.max}`);
        }
      }

      if (filters.isActive !== undefined) {
        filterParts.push(`isActive:${filters.isActive}`);
      }
    }

    return query;
  }
}
