import { Inject, Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigType } from '@nestjs/config';

import {
  SearchQuery,
  SearchResponse,
  SearchHit,
  IndexingOptions,
  BulkIndexingOptions,
} from '../interfaces/search.interface';
import { minisearchConfig, getDefaultSearchOptions } from '@/config/minisearch.config';
import { Product } from '@/modules/products/entities/product.entity';
import { SearchableProduct } from '../interfaces/product.interface';

type MiniSearchSettings = ConfigType<typeof minisearchConfig>;

@Injectable()
export class MiniSearchService implements OnModuleInit {
  private readonly logger = new Logger(MiniSearchService.name);
  private MiniSearch: any;
  private miniSearch: any;
  private isInitialized = false;

  constructor(
    @Inject(minisearchConfig.KEY)
    private readonly miniConfig: MiniSearchSettings,
  ) {}

  async onModuleInit(): Promise<void> {
    try {
      this.logger.debug('Initializing MiniSearch service...');
      await this.initializeClient();
      this.logger.log('MiniSearch service initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize MiniSearch service', error);
    }
  }

  private async initializeClient(): Promise<void> {
    try {
      const config = getDefaultSearchOptions(this.miniConfig);

      if (!this.MiniSearch) {
        const miniSearchModule = await import('minisearch');
        this.MiniSearch = miniSearchModule.default || miniSearchModule;
      }

      this.miniSearch = new this.MiniSearch({
        fields: config.searchableFields,
        storeFields: config.storeFields,
        idField: 'id',
        searchOptions: {
          boost: config.boost,
          fuzzy: config.fuzzy,
          prefix: config.prefix,
          combineWith: config.combineWith,
        },
        processTerm: (term: string): string | null => {
          const normalizedTerm = term.toLowerCase().trim();
          if (config?.stopWords?.includes(normalizedTerm) || normalizedTerm.length < 2) {
            return null;
          }
          return normalizedTerm;
        },
      });

      this.isInitialized = true;
      this.logger.debug('MiniSearch client initialized');
    } catch (error) {
      this.logger.error('Failed to initialize MiniSearch client', error);
      throw error;
    }
  }

  isReady(): boolean {
    return this.isInitialized && !!this.miniSearch;
  }

  getHealthStatus(): { status: string; details?: any } {
    try {
      if (!this.isReady()) {
        return {
          status: 'unhealthy',
          details: { error: 'MiniSearch not initialized' },
        };
      }

      const documentCount = this.miniSearch.documentCount;
      const termCount = this.miniSearch.termCount;

      return {
        status: 'healthy',
        details: {
          documentCount,
          termCount,
          isReady: this.isReady(),
        },
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        details: { error: error.message },
      };
    }
  }

  search<T = Product>(collectionName: string, query: SearchQuery): SearchResponse<T> {
    try {
      if (!this.isReady()) {
        throw new Error('MiniSearch service is not ready');
      }

      this.logger.debug(`Searching with query: ${query.q}`);

      const searchOptions = {
        fuzzy: this.miniConfig.search.fuzzyThreshold,
        boost: this.miniConfig.fields.boost,
        combineWith: 'AND' as const,
      };

      const results = this.miniSearch.search(query.q || '*', searchOptions);
      const page = query.page ?? 1;
      const limit = query.limit ?? 20;
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      const paginatedResults = results.slice(startIndex, endIndex);

      const hits: SearchHit<T>[] = paginatedResults.map((result: any) => ({
        document: result as T,
      }));

      return {
        found: results.length,
        hits,
        page,
      };
    } catch (error) {
      throw new Error(`Search failed in collection: ${collectionName}`, { cause: error });
    }
  }

  indexDocument(collectionName: string, document: SearchableProduct, _options?: IndexingOptions): void {
    try {
      if (!this.isReady()) {
        throw new Error('MiniSearch service is not ready');
      }

      this.logger.debug(`Indexing document: ${document.id}`);

      if (this.miniSearch.has(document.id)) {
        this.miniSearch.replace(document);
      } else {
        this.miniSearch.add(document);
      }

      this.logger.debug(`Document indexed successfully: ${document.id}`);
    } catch (error) {
      throw new Error(`Failed to index document: ${document.id}`, { cause: error });
    }
  }

  bulkIndexDocuments(collectionName: string, documents: SearchableProduct[], _options?: BulkIndexingOptions): any {
    try {
      if (!this.isReady()) {
        throw new Error('MiniSearch service is not ready');
      }

      this.logger.debug(`Bulk indexing ${documents.length} documents`);
      const results: Array<{ success: boolean; id: string; error?: string }> = [];
      let successCount = 0;
      let errorCount = 0;

      for (const document of documents) {
        try {
          if (this.miniSearch.has(document.id)) {
            this.miniSearch.replace(document);
          } else {
            this.miniSearch.add(document);
          }
          results.push({ success: true, id: document.id });
          successCount++;
        } catch (error: any) {
          results.push({ success: false, id: document.id, error: error.message });
          errorCount++;
        }
      }
      this.logger.debug(`Bulk indexing completed: ${successCount} success, ${errorCount} errors`);
      return {
        success: successCount,
        error: errorCount,
      };
    } catch (error) {
      throw new Error(`Bulk indexing failed in collection: ${collectionName}`, { cause: error });
    }
  }

  removeDocument(collectionName: string, documentId: string): void {
    try {
      if (!this.isReady()) {
        throw new Error('MiniSearch service is not ready');
      }

      this.logger.debug(`Removing document: ${documentId}`);

      if (this.miniSearch.has(documentId)) {
        this.miniSearch.discard(documentId);
        this.logger.debug(`Document removed successfully: ${documentId}`);
      } else {
        this.logger.warn(`Document not found for removal: ${documentId}`);
      }
    } catch (error) {
      throw new Error(`Failed to remove document: ${documentId}`, { cause: error });
    }
  }

  clearCollection(collectionName: string): void {
    try {
      if (!this.isReady()) {
        throw new Error('MiniSearch service is not ready');
      }

      this.logger.debug(`Clearing collection: ${collectionName}`);
      this.miniSearch.removeAll();
      this.logger.debug(`Collection cleared: ${collectionName}`);
    } catch (error) {
      throw new Error(`Failed to clear collection: ${collectionName}`, { cause: error });
    }
  }

  getCollectionStats(collectionName: string): any {
    try {
      if (!this.isReady()) {
        throw new Error('MiniSearch service is not ready');
      }

      return {
        name: collectionName,
        num_documents: this.miniSearch.documentCount,
        num_memory_shards: 1,
        fields: this.miniConfig.fields.searchableFields.map((field: string) => ({
          name: field,
          type: 'string',
          facet: false,
          index: true,
        })),
      };
    } catch (error) {
      throw new Error(`Failed to get collection stats: ${collectionName}`, { cause: error });
    }
  }

  getAllDocuments(): Product[] {
    if (!this.isReady()) {
      return [];
    }

    const results = this.miniSearch.search('*', { prefix: true });
    return results.map((result: any) => result as unknown as Product);
  }

  hasDocument(documentId: string): boolean {
    if (!this.isReady()) {
      return false;
    }
    return this.miniSearch.has(documentId);
  }

  getDocumentCount(): number {
    if (!this.isReady()) {
      return 0;
    }
    return this.miniSearch.documentCount;
  }
}
