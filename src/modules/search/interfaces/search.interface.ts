import { Product } from '@/modules/products/entities/product.entity';

export interface SearchQuery {
  q: string;
  page?: number;
  limit: number;
}

export interface SearchResponse<T = any> {
  found: number;
  page: number;
  hits: SearchHit<T>[];
  total_pages?: number;
  has_next_page?: boolean;
  has_prev_page?: boolean;
}

export interface SearchHit<T = any> {
  document: T;
}

export interface SearchHighlight {
  field: string;
  snippet: string;
  value?: string;
  matched_tokens: string[];
  indices?: number[];
}

export interface FacetCount {
  field_name: string;
  counts: FacetCountValue[];
  stats?: FacetStats;
}

export interface FacetCountValue {
  count: number;
  highlighted: string;
  value: string;
}

export interface FacetStats {
  avg?: number;
  max?: number;
  min?: number;
  sum?: number;
  total_values?: number;
}

export interface GroupedHit<T = any> {
  group_key: string[];
  hits: SearchHit<T>[];
}

export interface SearchRequestParams {
  collection_name: string;
  per_page: number;
  q: string;
}

export interface CollectionSchema {
  name: string;
  fields: CollectionField[];
  default_sorting_field?: string;
  enable_nested_fields?: boolean;
  token_separators?: string[];
  symbols_to_index?: string[];
  metadata?: Record<string, any>;
}

export interface CollectionField {
  name: string;
  type: FieldType;
  facet?: boolean;
  optional?: boolean;
  index?: boolean;
  sort?: boolean;
  infix?: boolean;
  locale?: string;
  stem?: boolean;
  reference?: string;
  range_index?: boolean;
  store?: boolean;
  embed?: EmbedConfig;
}

export type FieldType =
  | 'string'
  | 'int32'
  | 'int64'
  | 'float'
  | 'bool'
  | 'geopoint'
  | 'string[]'
  | 'int32[]'
  | 'int64[]'
  | 'float[]'
  | 'bool[]'
  | 'geopoint[]'
  | 'object'
  | 'object[]'
  | 'auto'
  | 'image';

export interface EmbedConfig {
  from: string[];
  model_config: {
    model_name: string;
    api_key?: string;
    indexing_prefix?: string;
    query_prefix?: string;
  };
}

export interface IndexingOptions {
  action?: 'create' | 'update' | 'upsert' | 'emplace';
  dirty_values?: 'coerce_or_reject' | 'coerce_or_drop' | 'drop' | 'reject';
  return_doc?: boolean;
  return_id?: boolean;
}

export interface BulkIndexingOptions extends IndexingOptions {
  batch_size?: number;
  return_doc?: boolean;
  return_id?: boolean;
}

export interface DeleteQuery {
  filter_by: string;
  batch_size?: number;
}

export interface UpdateQuery {
  filter_by: string;
  doc: Record<string, any>;
}

export interface SearchConfig {
  // MiniSearch configuration
  searchableFields: string[];
  storeFields: string[];
  boost: Record<string, number>;
  fuzzy: number;
  prefix: boolean;
  combineWith: 'AND' | 'OR';
  stopWords: string[];
  maxDocuments: number;
  enableSuggestions: boolean;
  enableAnalytics: boolean;
  cacheDuration: number;
}

export interface SearchException {
  message: string;
  httpCode?: number;
  searchError?: any;
}

export interface SearchMetrics {
  totalSearches: number;
  averageSearchTime: number;
  popularQueries: Array<{
    query: string;
    count: number;
  }>;
  searchErrors: number;
}
export interface SearchEventHandlers {
  onProductCreated(product: Product): Promise<void>;
  onProductUpdated(product: Product): Promise<void>;
  onProductDeleted(productId: string): void;
}

export interface SearchParam {
  q: string;
  page: number;
  limit: number;
}
