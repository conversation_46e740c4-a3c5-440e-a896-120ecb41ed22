export interface SearchableProduct {
  id: string;
  name: string;
  description?: string;
  price: number;
  tax: number;
  categoryId: string;
  categoryName: string;
  categoryPath: string[];
  imageUrl: string;
  isActive: boolean;
  createdAt: number;
  updatedAt: number;
  searchKeywords: string[];
}

export interface ProductIndexingResult {
  success: boolean;
  indexed: number;
  failed: number;
  errors: Array<{
    productId: string;
    error: string;
  }>;
  duration: number;
}
