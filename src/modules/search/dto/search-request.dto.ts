import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>al, <PERSON><PERSON><PERSON>ber, IsBoolean, IsArray, IsE<PERSON>, Min, <PERSON>, IsObject } from 'class-validator';
import { Type, Transform } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class SearchRequestDto {
  @ApiPropertyOptional({
    description: 'Search query string',
    example: 'laptop gaming',
  })
  @IsOptional()
  @IsString()
  q?: string;

  @ApiPropertyOptional({
    description: 'Page number for pagination',
    example: 1,
    minimum: 1,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Type(() => Number)
  page?: number = 1;

  @ApiPropertyOptional({
    description: 'Number of results per page',
    example: 20,
    minimum: 1,
    maximum: 100,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100)
  @Type(() => Number)
  limit?: number = 20;

  @ApiPropertyOptional({
    description: 'Sort field and direction',
    example: 'price:asc',
  })
  @IsOptional()
  @IsString()
  sort_by?: string;

  @ApiPropertyOptional({
    description: 'Enable faceted search',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  enable_facets?: boolean = false;

  @ApiPropertyOptional({
    description: 'Enable search result highlighting',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  enable_highlighting?: boolean = true;

  @ApiPropertyOptional({
    description: 'Number of typos to tolerate',
    example: 2,
    minimum: 0,
    maximum: 3,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(3)
  @Type(() => Number)
  num_typos?: number = 2;

  @ApiPropertyOptional({
    description: 'Enable prefix search',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  prefix?: boolean = true;

  @ApiPropertyOptional({
    description: 'Infix search mode',
    enum: ['off', 'always', 'fallback'],
    example: 'fallback',
  })
  @IsOptional()
  @IsEnum(['off', 'always', 'fallback'])
  infix?: 'off' | 'always' | 'fallback' = 'fallback';

  @ApiPropertyOptional({
    description: 'Fields to include in response',
    example: ['id', 'name', 'price', 'categoryName'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  include_fields?: string[];

  @ApiPropertyOptional({
    description: 'Fields to exclude from response',
    example: ['description', 'metaDescription'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  exclude_fields?: string[];

  @ApiPropertyOptional({
    description: 'Maximum number of facet values to return',
    example: 10,
    minimum: 1,
    maximum: 100,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100)
  @Type(() => Number)
  max_facet_values?: number = 10;

  @ApiPropertyOptional({
    description: 'Group results by field',
    example: 'categoryId',
  })
  @IsOptional()
  @IsString()
  group_by?: string;

  @ApiPropertyOptional({
    description: 'Maximum number of results per group',
    example: 3,
    minimum: 1,
    maximum: 10,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(10)
  @Type(() => Number)
  group_limit?: number = 3;

  @ApiPropertyOptional({
    description: 'Enable exhaustive search (slower but more accurate)',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  exhaustive_search?: boolean = false;

  @ApiPropertyOptional({
    description: 'Pinned document IDs to show at top',
    example: ['product-123', 'product-456'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  pinned_hits?: string[];

  @ApiPropertyOptional({
    description: 'Hidden document IDs to exclude from results',
    example: ['product-789'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  hidden_hits?: string[];

  @ApiPropertyOptional({
    description: 'Enable search overrides',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  enable_overrides?: boolean = true;

  @ApiPropertyOptional({
    description: 'Vector query for semantic search',
    example: '[0.1, 0.2, 0.3, ...]',
  })
  @IsOptional()
  @IsString()
  vector_query?: string;

  @ApiPropertyOptional({
    description: 'Timeout for remote embedding requests (ms)',
    example: 5000,
    minimum: 1000,
    maximum: 30000,
  })
  @IsOptional()
  @IsNumber()
  @Min(1000)
  @Max(30000)
  @Type(() => Number)
  remote_embedding_timeout_ms?: number = 5000;

  @ApiPropertyOptional({
    description: 'Number of retries for remote embedding requests',
    example: 3,
    minimum: 1,
    maximum: 10,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(10)
  @Type(() => Number)
  remote_embedding_num_tries?: number = 3;
}

export class AutocompleteRequestDto {
  @ApiProperty({
    description: 'Partial query string for autocomplete',
    example: 'lapt',
  })
  @IsString()
  q: string;

  @ApiPropertyOptional({
    description: 'Maximum number of suggestions',
    example: 10,
    minimum: 1,
    maximum: 20,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(20)
  @Type(() => Number)
  limit?: number = 10;

  @ApiPropertyOptional({
    description: 'Suggestion types to include',
    example: ['product', 'category', 'brand'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  types?: string[] = ['product', 'category', 'brand'];
}

export class BulkIndexRequestDto {
  @ApiProperty({
    description: 'Array of products to index',
    type: [Object],
  })
  @IsArray()
  @IsObject({ each: true })
  products: any[];

  @ApiPropertyOptional({
    description: 'Indexing action',
    enum: ['create', 'update', 'upsert', 'emplace'],
    example: 'upsert',
  })
  @IsOptional()
  @IsEnum(['create', 'update', 'upsert', 'emplace'])
  action?: 'create' | 'update' | 'upsert' | 'emplace' = 'upsert';

  @ApiPropertyOptional({
    description: 'Batch size for bulk operations',
    example: 100,
    minimum: 1,
    maximum: 1000,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(1000)
  @Type(() => Number)
  batch_size?: number = 100;

  @ApiPropertyOptional({
    description: 'Return indexed documents in response',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  return_doc?: boolean = false;

  @ApiPropertyOptional({
    description: 'Return document IDs in response',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  return_id?: boolean = true;
}
