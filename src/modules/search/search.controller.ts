import { Controller, Get, Query, UsePipes, ValidationPipe } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { PaginatedResult } from '@/common/dto/paginated-result.dto';
import { Product } from '@/modules/products/entities/product.entity';
import { Public } from '@/common/decorators/public.decorator';
import { ProductSearchService } from './services/product-search.service';
import { SearchRequestDto } from './dto/search-request.dto';
import { TransformResponse } from '@/common/decorators/transform-response.decorator';
@ApiTags('Search')
@Controller({ path: 'api/search', version: '1' })
@UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
export class SearchController {
  constructor(private readonly productSearchService: ProductSearchService) {}

  @Get('products')
  @Public()
  @TransformResponse(Product)
  searchProducts(@Query() query: SearchRequestDto): PaginatedResult<Product> {
    return this.productSearchService.searchProducts(query);
  }
}
