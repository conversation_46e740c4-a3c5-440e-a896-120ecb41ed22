import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';

import { SearchController } from './search.controller';
import { ProductSearchService } from './services/product-search.service';
import { MiniSearchService } from './services/minisearch.service';
import { minisearchConfig } from '@/config/minisearch.config';
import { Product } from '../products/entities/product.entity';
import { Category } from '../categories/entities/category.entity';
import { SearchEventHandlers } from './interfaces/search.interface';
import { ProductImportsModule } from '../product-import/product-imports.module';

@Module({
  imports: [
    ConfigModule.forFeature(minisearchConfig),
    TypeOrmModule.forFeature([Product, Category]),
    ProductImportsModule,
  ],
  controllers: [SearchController],
  providers: [
    MiniSearchService,
    ProductSearchService,
    {
      provide: 'SEARCH_EVENT_HANDLERS',
      useFactory: (productSearchService: ProductSearchService): SearchEventHandlers => {
        return {
          onProductCreated: async (product: Product): Promise<void> => {
            try {
              await productSearchService.indexProduct(product);
            } catch (error) {
              console.error('Failed to index product on creation', error);
            }
          },
          onProductUpdated: async (product: Product): Promise<void> => {
            try {
              await productSearchService.indexProduct(product);
            } catch (error) {
              console.error('Failed to update product index', error);
            }
          },
          onProductDeleted: (productId: string): void => {
            try {
              productSearchService.removeProduct(productId);
            } catch (error) {
              console.error('Failed to remove product from index', error);
            }
          },
        };
      },
      inject: [ProductSearchService],
    },
  ],
  exports: ['SEARCH_EVENT_HANDLERS'],
})
export class SearchModule {}
