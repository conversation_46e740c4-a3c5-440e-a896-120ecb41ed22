import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  HttpCode,
  HttpStatus,
  Query,
  UseInterceptors,
  ClassSerializerInterceptor,
  ParseIntPipe,
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';

import { UsersService } from './users.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { User } from './entities/user.entity';
import { PaginationDto } from '@/common/dto/pagination.dto';
import { PaginatedResult } from '@/common/dto/paginated-result.dto';
import { CreateUserDoc, DeleteUserDoc, GetAllUsersDoc, GetUserByIDDoc, UpdateUserDoc } from './docs/users.docs';
import { PaginationDoc, RestoreEntityDoc } from '@/common/docs/base.docs';
import { TransformResponse } from '@/common/decorators/transform-response.decorator';

@ApiTags('Users')
@Controller({ path: 'api/users', version: '1' })
@UseInterceptors(ClassSerializerInterceptor)
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Post()
  @CreateUserDoc()
  @HttpCode(HttpStatus.CREATED)
  @TransformResponse(CreateUserDto)
  async create(@Body() createUserDto: CreateUserDto): Promise<User> {
    return this.usersService.create(createUserDto);
  }

  @Get()
  @GetAllUsersDoc()
  @PaginationDoc()
  @TransformResponse(User)
  async findAll(
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('active') activeParam?: string,
  ): Promise<User[] | PaginatedResult<User>> {
    const active = activeParam === 'true' ? true : activeParam === 'false' ? false : undefined;

    const pagination = new PaginationDto();
    if (page) pagination.page = page;
    if (limit) pagination.limit = limit;

    if (pagination.page || pagination.limit) {
      if (active !== undefined) {
        return active
          ? this.usersService.getActiveUsersPaginated(pagination)
          : this.usersService.findAllPaginated(pagination);
      }
      return this.usersService.findAllPaginated(pagination);
    }

    if (active !== undefined) {
      return active ? this.usersService.getActiveUsers() : this.usersService.findAll();
    }
    return this.usersService.findAll();
  }

  @Get(':id')
  @GetUserByIDDoc()
  @TransformResponse(User)
  async findOne(@Param('id', ParseIntPipe) id: number): Promise<User> {
    return this.usersService.findOne(id);
  }

  @Patch(':id')
  @UpdateUserDoc()
  @TransformResponse(User)
  async update(@Param('id', ParseIntPipe) id: number, @Body() updateUserDto: UpdateUserDto): Promise<User> {
    return this.usersService.update(id, updateUserDto);
  }

  @Delete(':id')
  @DeleteUserDoc()
  @HttpCode(HttpStatus.NO_CONTENT)
  async remove(@Param('id', ParseIntPipe) id: number): Promise<void> {
    return this.usersService.remove(id);
  }

  @RestoreEntityDoc()
  @Patch(':id/restore')
  async restore(@Param('id', ParseIntPipe) id: number): Promise<User> {
    return this.usersService.restore(id);
  }
}
