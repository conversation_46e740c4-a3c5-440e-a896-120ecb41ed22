import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BaseService } from '@/common/base/base.service';
import { User } from './entities/user.entity';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { PaginationDto } from '@/common/dto/pagination.dto';
import { PaginatedResult } from '@/common/dto/paginated-result.dto';
import { UserErrors } from './users.errors';

@Injectable()
export class UsersService extends BaseService<User> {
  constructor(
    @InjectRepository(User)
    protected readonly userRepository: Repository<User>,
  ) {
    super(userRepository);
  }

  async create(createUserDto: CreateUserDto): Promise<User> {
    const existingUser = await this.findByUsername(createUserDto.username);
    if (existingUser) {
      throw UserErrors.USERNAME_ALREADY_EXISTS(createUserDto.username);
    }

    return super.create(createUserDto);
  }

  async findByUsername(username: string): Promise<User | null> {
    return this.userRepository.findOne({
      where: { username },
      withDeleted: false,
    });
  }

  async update(id: number, updateUserDto: UpdateUserDto): Promise<User> {
    if (updateUserDto.username) {
      const existingUser = await this.findByUsername(updateUserDto.username);
      if (existingUser && existingUser.id !== id) {
        throw UserErrors.USERNAME_ALREADY_EXISTS(updateUserDto.username);
      }
    }

    return super.update(id, updateUserDto);
  }

  async getActiveUsers(): Promise<User[]> {
    return this.userRepository.find({
      where: { isActive: true },
      withDeleted: false,
    });
  }

  async getActiveUsersPaginated(pagination: PaginationDto): Promise<PaginatedResult<User>> {
    const [users, total] = await this.userRepository.findAndCount({
      where: { isActive: true },
      skip: pagination.skip,
      take: pagination.limit,
      withDeleted: false,
    });

    return new PaginatedResult(users, total, pagination.page, pagination.limit);
  }

  async validatePassword(user: User, password: string): Promise<boolean> {
    return user.validatePassword(password);
  }

  async updatePassword(id: number, hashedPassword: string): Promise<void> {
    await this.userRepository.update(id, { password: hashedPassword });
  }

  async updateRefreshToken(id: number, refreshToken: string | null): Promise<void> {
    await this.userRepository.update(id, { refreshToken: refreshToken ?? '' });
  }
}
