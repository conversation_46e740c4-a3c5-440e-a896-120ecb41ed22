import * as bcrypt from 'bcryptjs';
import { Exclude, Expose } from 'class-transformer';
import { Entity, Column, Index, BeforeInsert, BeforeUpdate, PrimaryGeneratedColumn } from 'typeorm';
import { BaseEntity } from '@/common/base/base.entity';

@Entity('users')
export class User extends BaseEntity {
  @Expose()
  @PrimaryGeneratedColumn()
  id: number;

  @Expose()
  @Column({ type: 'varchar', length: 255, unique: true })
  @Index('IDX_USER_USERNAME', { unique: true })
  username: string;

  @Exclude()
  @Column({ type: 'varchar', length: 255 })
  password: string;

  @Expose()
  @Column({ type: 'boolean', default: true })
  @Index('IDX_USER_ACTIVE')
  isActive: boolean;

  @Column({ nullable: true })
  refreshToken?: string;

  @BeforeInsert()
  @BeforeUpdate()
  async hashPassword(): Promise<void> {
    if (this.password) {
      const saltRounds = parseInt(process.env.BCRYPT_SALT_ROUNDS ?? '12', 10);
      this.password = await bcrypt.hash(this.password, saltRounds);
    }
  }

  async validatePassword(password: string): Promise<boolean> {
    return bcrypt.compare(password, this.password);
  }
}
