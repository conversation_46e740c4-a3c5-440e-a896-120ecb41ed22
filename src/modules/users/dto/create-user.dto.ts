import { i18nValidationMessage } from 'nestjs-i18n';
import { IsString, IsOptional, IsBoolean, Min<PERSON>ength, <PERSON><PERSON><PERSON>th, IsNotEmpty, Matches } from 'class-validator';

export class CreateUserDto {
  @IsString({ message: i18nValidationMessage('validation.common.mustBeString') })
  @IsNotEmpty({ message: i18nValidationMessage('validation.common.required') })
  @MinLength(3, { message: i18nValidationMessage('validation.specific.username.minLength', { min: 3 }) })
  @MaxLength(50, { message: i18nValidationMessage('validation.specific.username.maxLength', { max: 50 }) })
  @Matches(/^[a-zA-Z0-9_]+$/, {
    message: i18nValidationMessage('validation.specific.username.invalid'),
  })
  username: string;

  @IsString()
  @IsNotEmpty({ message: i18nValidationMessage('validation.common.required') })
  @MinLength(6, {
    message: i18nValidationMessage('validation.specific.password.minLength', { min: 6 }),
  })
  password: string;

  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}
