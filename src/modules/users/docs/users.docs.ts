import { applyDecorators, HttpStatus } from '@nestjs/common';
import { ApiOperation, ApiBody, ApiResponse, ApiBadRequestResponse, ApiQuery, ApiParam } from '@nestjs/swagger';
import { User } from '../entities/user.entity';
import { CreateUserDto } from '../dto/create-user.dto';

export function CreateUserDoc(): MethodDecorator {
  return applyDecorators(
    ApiOperation({ summary: 'Create a new user' }),
    ApiBody({ type: CreateUserDto }),
    ApiResponse({ status: HttpStatus.CREATED, description: 'User created successfully', type: User }),
    ApiBadRequestResponse({ description: 'Validation error' }),
  );
}

export function GetAllUsersDoc(): MethodDecorator {
  return applyDecorators(
    ApiOperation({ summary: 'Get all users' }),
    ApiQuery({ name: 'active', type: Boolean, required: false }),
    ApiResponse({ status: HttpStatus.OK, description: 'Users retrieved successfully', type: [User] }),
  );
}

export function GetUserByIDDoc(): MethodDecorator {
  return applyDecorators(
    ApiOperation({ summary: 'Get user by ID' }),
    ApiParam({ name: 'id', description: 'User ID' }),
    ApiResponse({ status: HttpStatus.OK, description: 'User retrieved successfully', type: User }),
    ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'User not found' }),
  );
}

export function UpdateUserDoc(): MethodDecorator {
  return applyDecorators(
    ApiOperation({
      summary: 'Update user',
      description: 'Update any user fields including name, email, role, isActive, etc.',
    }),
    ApiParam({ name: 'id', description: 'User ID' }),
    ApiResponse({ status: HttpStatus.OK, description: 'User updated successfully', type: User }),
    ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'User not found' }),
  );
}
export function DeleteUserDoc(): MethodDecorator {
  return applyDecorators(
    ApiOperation({ summary: 'Delete user' }),
    ApiParam({ name: 'id', description: 'User ID' }),
    ApiResponse({ status: HttpStatus.NO_CONTENT, description: 'User deleted successfully' }),
    ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'User not found' }),
  );
}
